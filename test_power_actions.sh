// Simplified create_cloud_servers_for_order that uses IDs directly from orders_items
function create_cloud_servers_for_order($pdo, $order_id) {
    error_log("=== CREATE CLOUD SERVERS FOR ORDER $order_id ===", 3, "/tmp/api_debug.log");
    error_log("DEBUG: Starting cloud server creation process", 3, "/tmp/api_debug.log");

    $servers_created = [];
    $servers_failed = [];

    try {
        // Get order details
        error_log("DEBUG: Fetching order details for order_id: $order_id", 3, "/tmp/api_debug.log");
        $order_query = "SELECT o.*, o.order_type
                       FROM orders o
                       WHERE o.id = :order_id";

        $order_stmt = $pdo->prepare($order_query);
        $order_stmt->bindValue(":order_id", $order_id);
        $order_stmt->execute();

        if ($order_stmt->rowCount() == 0) {
            error_log("ERROR: Order not found: $order_id", 3, "/tmp/api_debug.log");
            return [false, []];
        }

        $order = $order_stmt->fetch(PDO::FETCH_ASSOC);
        error_log("DEBUG: Order data: " . json_encode($order), 3, "/tmp/api_debug.log");

        // Only proceed for cloud orders
        if ($order["order_type"] != "cloud") {
            error_log("INFO: Not a cloud order (type: " . $order["order_type"] . "), skipping cloud server creation", 3, "/tmp/api_debug.log");
            return [false, []];
        }

        // Get all order items - SIMPLIFIED QUERY
        error_log("DEBUG: Fetching order items for cloud servers", 3, "/tmp/api_debug.log");
        $items_query = "SELECT * FROM orders_items
                       WHERE order_id = :order_id
                       AND type = 'cloud'
                       AND (server_id IS NULL OR server_id = 0)";

        $items_stmt = $pdo->prepare($items_query);
        $items_stmt->bindValue(":order_id", $order_id);
        $items_stmt->execute();

        if ($items_stmt->rowCount() == 0) {
            error_log("INFO: No cloud servers to create for order $order_id", 3, "/tmp/api_debug.log");
            return [false, []];
        }

        $order_items = $items_stmt->fetchAll(PDO::FETCH_ASSOC);
        error_log("DEBUG: Found " . count($order_items) . " cloud servers to create", 3, "/tmp/api_debug.log");
        
        // Initialize SolusVM API
        $apiKey = "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
        $baseUrl = "https://virt.zetservers.com";
        
        // Process each order item
        foreach ($order_items as $index => $item) {
            try {
                error_log("DEBUG: Processing server #" . ($index + 1) . ": " . $item['hostname'], 3, "/tmp/api_debug.log");
                error_log("DEBUG: Item details: " . json_encode($item), 3, "/tmp/api_debug.log");

                // Create server via SolusVM API
                $solus_server_id = create_solus_server_direct($apiKey, $baseUrl, $item);

                if ($solus_server_id) {
                    error_log("SUCCESS: Created cloud server in SolusVM with ID: $solus_server_id", 3, "/tmp/api_debug.log");

                    // Update order_items with the SolusVM server_id
                    $update_sql = "UPDATE orders_items
                                  SET server_id = :server_id,
                                      status = 'Active'
                                  WHERE id = :item_id";

                    $update_stmt = $pdo->prepare($update_sql);
                    $update_stmt->bindValue(":server_id", $solus_server_id);
                    $update_stmt->bindValue(":item_id", $item['id']);
                    $update_stmt->execute();

                    $servers_created[] = $solus_server_id;

                    // Log the creation
                    $log_sql = "INSERT INTO activity_log
                               (user_id, action, description, user_name, activity_type)
                               VALUES
                               (:user_id, :action, :description, :user_name, 'server_provisioning')";

                    $log_stmt = $pdo->prepare($log_sql);
                    $log_stmt->bindValue(":user_id", $order['owner_id']);
                    $log_stmt->bindValue(":action", "cloud_server_created");
                    $log_stmt->bindValue(":description", "Cloud server {$item['hostname']} (SolusVM ID: $solus_server_id) created successfully");
                    $log_stmt->bindValue(":user_name", "System");
                    $log_stmt->execute();

                } else {
                    error_log("ERROR: Failed to create cloud server in SolusVM: " . $item['hostname'], 3, "/tmp/api_debug.log");
                    $servers_failed[] = $item['hostname'];
                }

            } catch (Exception $e) {
                error_log("ERROR: Exception creating server " . $item['hostname'] . ": " . $e->getMessage(), 3, "/tmp/api_debug.log");
                $servers_failed[] = $item['hostname'];
            }
        }

        // Update order status
        if (count($servers_created) > 0) {
            $status = count($servers_failed) > 0 ? 'Partially Active' : 'Active';
            $update_order_sql = "UPDATE orders
                               SET status = :status,
                                   allocation_date = NOW()
                               WHERE id = :order_id";

            $update_order_stmt = $pdo->prepare($update_order_sql);
            $update_order_stmt->bindValue(":status", $status);
            $update_order_stmt->bindValue(":order_id", $order_id);
            $update_order_stmt->execute();
        }

        $success = count($servers_created) > 0;
        error_log("=== CLOUD SERVER CREATION COMPLETED ===", 3, "/tmp/api_debug.log");
        error_log("Summary - Created: " . count($servers_created) . ", Failed: " . count($servers_failed), 3, "/tmp/api_debug.log");

        return [$success, $servers_created];

    } catch (Exception $e) {
        error_log("FATAL ERROR in create_cloud_servers_for_order: " . $e->getMessage(), 3, "/tmp/api_debug.log");
        return [false, []];
    }
}

// Direct server creation function - uses IDs directly from orders_items
function create_solus_server_direct($apiKey, $baseUrl, $order_item) {
    try {
        error_log("DEBUG: Creating server in SolusVM using direct IDs", 3, "/tmp/api_debug.log");
        error_log("DEBUG: Order item data: " . json_encode($order_item), 3, "/tmp/api_debug.log");

        // Project ID from diagnostic output
        $project_id = 3; // From your diagnostic: "ID: 3 | Name: Default project"
        
        // Use IDs directly from orders_items
        $plan_id = intval($order_item['config_id']);      // config_id = SolusVM plan ID
        $location_id = intval($order_item['location_id']); // location_id = SolusVM location ID
        $os_version_id = intval($order_item['os_id']);     // os_id = SolusVM OS version ID
        $num_ips = intval($order_item['subnet_id']);       // subnet_id = number of IPs

        error_log("DEBUG: Using SolusVM IDs - Plan: $plan_id, Location: $location_id, OS: $os_version_id, IPs: $num_ips", 3, "/tmp/api_debug.log");

        // Prepare the SolusVM API data structure
        $solus_data = [
            "name" => $order_item['hostname'],
            "plan_id" => $plan_id,
            "location_id" => $location_id,
            "os_image_version_id" => $os_version_id,
            "password" => $order_item['password'],
            "application_id" => null,
            "application_data" => [],
            "ssh_keys" => [],
            "user_data" => null,
            "fqdns" => [],
            "backup_settings" => [
                "enabled" => false,
                "schedule" => [
                    "frequency" => [
                        "type" => "weekly",
                        "value" => []
                    ],
                    "time" => "12:00"
                ],
                "limit" => [
                    "value" => 1,
                    "is_enabled" => false,
                    "unit" => "count"
                ]
            ],
            "primary_disk_offer_index" => 0,
            "additional_disks" => [],
            "ip_types" => ["ipv4"],
            "vpc_network_ids" => []
        ];

        // Add additional IPs if subnet_id > 1
        if ($num_ips > 1) {
            $solus_data['additional_ipv4'] = $num_ips - 1;
            error_log("DEBUG: Adding " . ($num_ips - 1) . " additional IPv4 addresses", 3, "/tmp/api_debug.log");
        }

        error_log("DEBUG: Final SolusVM API data: " . json_encode($solus_data), 3, "/tmp/api_debug.log");

        // Make the API request
        $url = $baseUrl . "/api/v1/projects/{$project_id}/servers";
        
        $headers = [
            'Authorization: Bearer ' . $apiKey,
            'Content-Type: application/json',
            'Accept: application/json'
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($solus_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        error_log("DEBUG: Making SolusVM API request to: $url", 3, "/tmp/api_debug.log");

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);

        curl_close($ch);

        error_log("DEBUG: SolusVM API response code: $http_code", 3, "/tmp/api_debug.log");
        error_log("DEBUG: SolusVM API response: $response", 3, "/tmp/api_debug.log");

        if ($curl_error) {
            error_log("ERROR: cURL error: $curl_error", 3, "/tmp/api_debug.log");
            return false;
        }

        if ($http_code === 201 || $http_code === 200) {
            $response_data = json_decode($response, true);

            if (isset($response_data['data']['id'])) {
                $server_id = $response_data['data']['id'];
                error_log("SUCCESS: SolusVM server created with ID: $server_id", 3, "/tmp/api_debug.log");
                return $server_id;
            } elseif (isset($response_data['id'])) {
                $server_id = $response_data['id'];
                error_log("SUCCESS: SolusVM server created with ID: $server_id", 3, "/tmp/api_debug.log");
                return $server_id;
            } else {
                error_log("ERROR: No server ID found in SolusVM response", 3, "/tmp/api_debug.log");
                return false;
            }
        } else {
            error_log("ERROR: SolusVM API returned HTTP $http_code", 3, "/tmp/api_debug.log");
            
            $response_data = json_decode($response, true);
            if (isset($response_data['message'])) {
                error_log("ERROR: SolusVM error message: " . $response_data['message'], 3, "/tmp/api_debug.log");
            }
            if (isset($response_data['errors'])) {
                error_log("ERROR: SolusVM errors: " . json_encode($response_data['errors']), 3, "/tmp/api_debug.log");
            }

            return false;
        }

    } catch (Exception $e) {
        error_log("ERROR: Exception in create_solus_server_direct: " . $e->getMessage(), 3, "/tmp/api_debug.log");
        return false;
    }
}

// Test function to verify the server creation payload
function test_server_creation_payload($pdo, $order_id) {
    echo "=== Testing Server Creation Payload ===\n\n";
    
    // Get order item
    $stmt = $pdo->prepare("SELECT * FROM orders_items WHERE order_id = :order_id AND type = 'cloud' LIMIT 1");
    $stmt->bindValue(":order_id", $order_id);
    $stmt->execute();
    $item = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$item) {
        echo "No cloud order items found for order $order_id\n";
        return;
    }
    
    echo "Order Item Data:\n";
    echo "- Hostname: {$item['hostname']}\n";
    echo "- Config ID (Plan): {$item['config_id']}\n";
    echo "- Location ID: {$item['location_id']}\n";
    echo "- OS ID: {$item['os_id']}\n";
    echo "- Subnet ID (IPs): {$item['subnet_id']}\n";
    echo "- Password: {$item['password']}\n\n";
    
    // Build the payload
    $solus_data = [
        "name" => $item['hostname'],
        "plan_id" => intval($item['config_id']),
        "location_id" => intval($item['location_id']),
        "os_image_version_id" => intval($item['os_id']),
        "password" => $item['password'],
        "application_id" => null,
        "application_data" => [],
        "ssh_keys" => [],
        "user_data" => null,
        "fqdns" => [],
        "backup_settings" => [
            "enabled" => false,
            "schedule" => [
                "frequency" => [
                    "type" => "weekly",
                    "value" => []
                ],
                "time" => "12:00"
            ],
            "limit" => [
                "value" => 1,
                "is_enabled" => false,
                "unit" => "count"
            ]
        ],
        "primary_disk_offer_index" => 0,
        "additional_disks" => [],
        "ip_types" => ["ipv4"],
        "vpc_network_ids" => []
    ];
    
    if (intval($item['subnet_id']) > 1) {
        $solus_data['additional_ipv4'] = intval($item['subnet_id']) - 1;
    }
    
    echo "Generated Payload:\n";
    echo json_encode($solus_data, JSON_PRETTY_PRINT);
    echo "\n\nEndpoint: https://virt.zetservers.com/api/v1/projects/3/servers\n";
}