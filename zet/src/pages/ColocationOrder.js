import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { <PERSON>, CardBody, Row, Col, Container, <PERSON><PERSON>, Modal, <PERSON>dalHeader, <PERSON>dal<PERSON><PERSON>, <PERSON><PERSON><PERSON>oot<PERSON>, <PERSON><PERSON>, Spinner } from 'reactstrap';
import PaymentModal from './PaymentModal.js';

const ColocationOrder = () => {
  // State for configurations
  const [rackOptions, setRackOptions] = useState([]);
  const [powerOptions, setPowerOptions] = useState([]);
  const [bandwidthOptions, setBandwidthOptions] = useState([]);
  const [locationOptions, setLocationOptions] = useState([]);
  const [subnetOptions, setSubnetOptions] = useState([]);
  const [contractOptions, setContractOptions] = useState([]);

  // State for selections
  const [selectedRack, setSelectedRack] = useState(null);
  const [selectedPower, setSelectedPower] = useState(null);
  const [selectedBandwidth, setSelectedBandwidth] = useState(null);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [selectedSubnet, setSelectedSubnet] = useState(null);
  const [selectedContract, setSelectedContract] = useState(null);
  const [termsAccepted, setTermsAccepted] = useState(false);
  
  // State for UI
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [totalPrice, setTotalPrice] = useState(0);
  const [modalTotalPrice, setModalTotalPrice] = useState(0);
  const [deliveryTime, setDeliveryTime] = useState('');
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [orderSuccess, setOrderSuccess] = useState(false);
  const [orderId, setOrderId] = useState(null);
  
  // Temporary state for modal selections
  const [tempSubnet, setTempSubnet] = useState(null);
  const [tempContract, setTempContract] = useState(null);

  const navigate = useNavigate();

  // Function to get token from session storage
  function getToken() {
    return sessionStorage.getItem('token');
  }
  
  // Fetch configuration data from the API
  useEffect(() => {
    fetchColocationOrderConfig();
  }, []);

  // Fetch colocation order configuration
  const fetchColocationOrderConfig = async (params = {}) => {
    setLoading(true);
    setError(null);
    
    const token = getToken();
    if (!token) {
      setError('Authentication token not found. Please log in again.');
      setLoading(false);
      return;
    }
    
    // Construct the query string with any parameters
    const queryParams = new URLSearchParams({
      f: 'colocationorder',
      ...params
    }).toString();
    
    try {
      console.log(`Fetching config with params: ${queryParams}`);
      
      const response = await fetch(`/New_client/api.php?${queryParams}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      
      // Get the raw text first to inspect it
      const rawText = await response.text();
      console.log('Raw API response:', rawText);
      
      // Try to parse the response as JSON
      let data;
      try {
        data = JSON.parse(rawText);
      } catch (parseError) {
        console.error('JSON parse error:', parseError);
        throw new Error(`Invalid JSON response: ${rawText.substring(0, 100)}...`);
      }
      
      if (data.error) {
        if (data.error === 5) {
          alert('ERROR: Your login session has timed out');
          window.location.reload(false);
        } else {
          setError(`ERROR: ${data.error}`);
        }
      } else {
        if (Array.isArray(data) && data.length > 0) {
          processConfigData(data[0]);
        } else {
          throw new Error('Invalid data structure received from API');
        }
      }
    } catch (err) {
      console.error('Fetch error:', err);
      setError(`Failed to fetch configuration data: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Process the configuration data
  const processConfigData = (data) => {
    if (!data) return;
    
    // Set options
    setRackOptions(data.rack || []);
    setPowerOptions(data.power || []);
    setBandwidthOptions(data.bandwidth || []);
    setLocationOptions(data.location || []);
    setSubnetOptions(data.subnet || []);
    setContractOptions(data.contract || []);
    
    // Set initial selections
    const initialRack = data.rack?.find(rack => rack.checked);
    const initialPower = data.power?.find(power => power.checked);
    const initialBandwidth = data.bandwidth?.find(bandwidth => bandwidth.checked);
    const initialLocation = data.location?.find(location => location.checked);
    const initialSubnet = data.subnet?.find(subnet => subnet.checked);
    const initialContract = data.contract?.find(contract => contract.checked);
    
    setSelectedRack(initialRack || null);
    setSelectedPower(initialPower || null);
    setSelectedBandwidth(initialBandwidth || null);
    setSelectedLocation(initialLocation || null);
    setSelectedSubnet(initialSubnet || null);
    setSelectedContract(initialContract || null);
    
    // Set price and delivery
    setTotalPrice(data.price || 0);
    setDeliveryTime(data.delivery || '');
  };

  // Update selections for a specific category
  const updateSelection = async (category, selection) => {
    // Update the local state first for responsive UI
    switch (category) {
      case 'rack':
        setSelectedRack(selection);
        break;
      case 'power':
        setSelectedPower(selection);
        break;
      case 'bandwidth':
        setSelectedBandwidth(selection);
        break;
      case 'location':
        setSelectedLocation(selection);
        break;
      case 'subnet':
        setSelectedSubnet(selection);
        break;
      case 'contract':
        setSelectedContract(selection);
        break;
      default:
        break;
    }
    
    // Fetch updated configuration based on new selection
    const params = {};
    if (selectedRack) params.rack_id = selectedRack.id;
    if (selectedPower) params.power_id = selectedPower.id;
    if (selectedBandwidth) params.bandwidth_id = selectedBandwidth.id;
    if (selectedLocation) params.location_id = selectedLocation.id;
    if (selectedSubnet) params.subnet_id = selectedSubnet.id;
    if (selectedContract) params.contract_id = selectedContract.id;
    
    // Override with the new selection
    params[`${category}_id`] = selection.id;
    
    // Fetch updated configuration
    await fetchColocationOrderConfig(params);
  };

  // Handle rack change
  const handleRackChange = (rack) => {
    updateSelection('rack', rack);
  };

  // Handle power change
  const handlePowerChange = (power) => {
    updateSelection('power', power);
  };

  // Handle bandwidth change
  const handleBandwidthChange = (bandwidth) => {
    updateSelection('bandwidth', bandwidth);
  };

  // Handle location change
  const handleLocationChange = (location) => {
    updateSelection('location', location);
  };

  // Handle subnet change
  const handleSubnetChange = (subnet) => {
    updateSelection('subnet', subnet);
  };

  // Handle contract change
  const handleContractChange = (contract) => {
    updateSelection('contract', contract);
  };

  // Toggle confirmation modal
  const toggleConfirmModal = () => {
    if (showConfirmModal) {
      // When closing the modal, reset temporary values
      setTempSubnet(null);
      setTempContract(null);
    } else {
      // When opening the modal, initialize temp values with current selections
      setTempSubnet(selectedSubnet);
      setTempContract(selectedContract);
      
      // Initialize modal price with current total price
      setModalTotalPrice(totalPrice);
    }
    setShowConfirmModal(!showConfirmModal);
  };

  // Toggle payment modal
  const togglePaymentModal = () => {
    setShowPaymentModal(!showPaymentModal);
  };

  // Handle order button click
  const handleOrderClick = () => {
    // Validate required selections
    if (!selectedRack || !selectedPower || !selectedBandwidth || !selectedLocation || !selectedSubnet || !selectedContract) {
      setError('Please select all required options before ordering.');
      return;
    }
    
    // Open the confirmation modal
    toggleConfirmModal();
  };
  
  // Handle temporary subnet change (only within modal)
  const handleTempSubnetChange = (subnet) => {
    setTempSubnet(subnet);
    
    // Update modal price when subnet changes
    let newPrice = totalPrice;
    if (subnet.price && selectedSubnet.price) {
      newPrice = totalPrice - Number(selectedSubnet.price) + Number(subnet.price);
    }
    setModalTotalPrice(newPrice);
  };
  
  // Handle temporary contract change (only within modal)
  const handleTempContractChange = (contract) => {
    setTempContract(contract);
    
    // Update modal price when contract changes
    // Note: In the real implementation, you'd need to determine how contract changes affect price
    setModalTotalPrice(totalPrice);
  };

  // Place order with the API
  const placeColocationOrder = async () => {
    setLoading(true);
    setError(null);
    
    const token = getToken();
    if (!token) {
      setError('Authentication token not found. Please log in again.');
      setLoading(false);
      return;
    }
    
    const orderParams = new URLSearchParams({
      f: 'placecolocationorder',
      rack_id: selectedRack.id,
      power_id: selectedPower.id,
      bandwidth_id: selectedBandwidth.id,
      location_id: selectedLocation.id,
      subnet_id: tempSubnet ? tempSubnet.id : selectedSubnet.id,
      contract_id: tempContract ? tempContract.id : selectedContract.id
    }).toString();
    
    try {
      const response = await fetch(`/New_client/api.php?${orderParams}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.error) {
        if (data.error === 5) {
          alert('ERROR: Your login session has timed out');
          window.location.reload(false);
        } else {
          setError(`ERROR: ${data.error}`);
        }
      } else {
        // Order successfully placed
        setOrderSuccess(true);
        setOrderId(data.orderid);
        // Close confirmation modal
        toggleConfirmModal();
        // Open payment modal
        togglePaymentModal();
      }
    } catch (err) {
      setError(`Failed to place order: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Handle order submission
  const handleOrder = () => {
    if (!termsAccepted) {
      return; // Exit if terms not accepted
    }
    
    // Apply the temporary selections to the actual state
    if (tempSubnet) setSelectedSubnet(tempSubnet);
    if (tempContract) setSelectedContract(tempContract);
    
    // Place the order
    placeColocationOrder();
  };

  // Handle payment completion
  const handlePaymentComplete = () => {
    // Close the payment modal
    togglePaymentModal();
    
    // Navigate to the server list/dashboard
    navigate('/');
  };

  // Render loading spinner
  if (loading && !selectedRack) {
    return (
      <Container fluid className="p-0">
        <div className="page-header">
          <div className="page-leftheader">
            <ol className="breadcrumb">
              <li className="breadcrumb-item1"><Link to="/">Home</Link></li>
              <li className="breadcrumb-item1">New Colocation</li>
            </ol>
          </div>
        </div>
        <div className="text-center my-5 py-5">
          <Spinner color="primary" />
        </div>
      </Container>
    );
  }

  // Render error state
  if (error && !selectedRack) {
    return (
      <Container fluid className="p-0">
        <div className="page-header">
          <div className="page-leftheader">
            <ol className="breadcrumb">
              <li className="breadcrumb-item1"><Link to="/">Home</Link></li>
              <li className="breadcrumb-item1">New Colocation</li>
            </ol>
          </div>
        </div>
        <Alert color="danger" className="my-4">
          <i className="fa fa-exclamation-circle me-2"></i>
          <strong>Error:</strong> {error}
          <div className="mt-3">
            <Button color="primary" onClick={() => fetchColocationOrderConfig()}>
              Try Again
            </Button>
          </div>
        </Alert>
      </Container>
    );
  }

  return (
    <Container fluid className="p-0">
      {/* Page Header */}
      <div className="page-header">
        <div className="page-leftheader">
          <ol className="breadcrumb">
            <li className="breadcrumb-item1"><Link to="/">Home</Link></li>
            <li className="breadcrumb-item1">New Colocation</li>
          </ol>
        </div>
        <div className="page-rightheader ml-auto">
          <div className="dropdown">
            <a href="#" className="nav-link pr-0 leading-none" data-toggle="dropdown">
              <button className="btn btn-success">New Service</button>
            </a>
            <div className="dropdown-menu">
              <Link className="dropdown-item d-flex" to="/dedicatedorder">
                <div className="mt-1">Dedicated Server</div>
              </Link>
              <Link className="dropdown-item d-flex" to="/cloudorder">
                <div className="mt-1">Cloud Server</div>
              </Link>

            </div>
          </div>
        </div>
      </div>

      <Alert color="success" className="mb-4">
        <i className="fa fa-check-circle-o me-2" aria-hidden="true"></i> <strong>Well done!</strong> Activate another colocation service to get <b>15% discount</b> on all colocation &nbsp; <Link to="/reseller"><button className="btn btn-default btn-sm">Check Discounts</button></Link>
      </Alert>

      {/* Error Alert */}
      {error && (
        <Alert color="danger" className="mb-4">
          <i className="fa fa-exclamation-circle me-2"></i>
          <strong>Error:</strong> {error}
        </Alert>
      )}

      <Card className="shadow-sm mb-4">
        <CardBody>
          <h2 className="card-title text-primary mb-4">Configure Your Colocation</h2>
          
          <Row className="g-4 mb-4">
            {/* Rack Size Selection */}
            <Col md={12} lg={2}>
              <Card className="h-100 border">
                <div className="card-header bg-light">
                  <h5 className="mb-0">
                    <i className="fa fa-server text-primary me-2" style={{paddingRight: '0.5rem'}}></i>
                    Rack Size
                  </h5>
                </div>
                <CardBody className="p-0">
                  <div className="better-radio-group">
                    {rackOptions.map(rack => (
                      <div key={rack.id} className="position-relative">
                        <input
                          type="radio"
                          id={`rack_${rack.id}`}
                          name="rackSize"
                          className="better-radio"
                          checked={selectedRack && selectedRack.id === rack.id}
                          onChange={() => handleRackChange(rack)}
                        />
                        <label 
                          htmlFor={`rack_${rack.id}`} 
                          className={`list-group-item d-flex flex-column p-3 ${selectedRack && selectedRack.id === rack.id ? 'selected' : ''}`}
                        >
                          <div className="fw-bold">{rack.size}</div>
                          <div className="text-success">
                            {rack.price > 0 ? `+€${rack.price}` : 'Included'}
                          </div>
                        </label>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </Col>

            {/* Power Selection */}
            <Col md={12} lg={2}>
              <Card className="h-100 border">
                <div className="card-header bg-light">
                  <h5 className="mb-0">
                    <i className="fa fa-bolt text-primary me-2" style={{paddingRight: '0.5rem'}}></i>
                    Power
                  </h5>
                </div>
                <CardBody className="p-0">
                  <div className="better-radio-group">
                    {powerOptions.map(power => (
                      <div key={power.id} className="position-relative">
                        <input
                          type="radio"
                          id={`power_${power.id}`}
                          name="power"
                          className="better-radio"
                          checked={selectedPower && selectedPower.id === power.id}
                          onChange={() => handlePowerChange(power)}
                        />
                        <label 
                          htmlFor={`power_${power.id}`} 
                          className={`list-group-item d-flex flex-column p-3 ${selectedPower && selectedPower.id === power.id ? 'selected' : ''}`}
                        >
                          <div className="fw-bold">{power.name}</div>
                          <div className="text-success">
                            {power.price > 0 ? `+€${power.price}` : 'Included'}
                          </div>
                        </label>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </Col>

            {/* Bandwidth Selection */}
            <Col md={12} lg={2}>
              <Card className="h-100 border">
                <div className="card-header bg-light">
                  <h5 className="mb-0">
                    <i className="fa fa-tachometer text-primary me-2" style={{paddingRight: '0.5rem'}}></i>
                    Bandwidth
                  </h5>
                </div>
                <CardBody className="p-0">
                  <div className="better-radio-group">
                    {bandwidthOptions.map(bandwidth => (
                      <div key={bandwidth.id} className="position-relative">
                        <input
                          type="radio"
                          id={`bandwidth_${bandwidth.id}`}
                          name="bandwidth"
                          className="better-radio"
                          checked={selectedBandwidth && selectedBandwidth.id === bandwidth.id}
                          onChange={() => handleBandwidthChange(bandwidth)}
                        />
                        <label 
                          htmlFor={`bandwidth_${bandwidth.id}`} 
                          className={`list-group-item d-flex flex-column p-3 ${selectedBandwidth && selectedBandwidth.id === bandwidth.id ? 'selected' : ''}`}
                        >
                          <div className="fw-bold">{bandwidth.name}</div>
                          <div className="text-success">
                            {bandwidth.price > 0 ? `+€${bandwidth.price}` : 'Included'}
                          </div>
                        </label>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </Col>

            {/* Location Selection */}
            <Col md={12} lg={2}>
              <Card className="h-100 border">
                <div className="card-header bg-light">
                  <h5 className="mb-0">
                    <i className="fa fa-map-marker text-primary me-2" style={{paddingRight: '0.5rem'}}></i>
                    Location
                  </h5>
                </div>
                <CardBody className="p-0">
                  <div className="better-radio-group">
                    {locationOptions.map(location => (
                      <div key={location.id} className="position-relative">
                        <input
                          type="radio"
                          id={`location_${location.id}`}
                          name="location"
                          className="better-radio"
                          checked={selectedLocation && selectedLocation.id === location.id}
                          onChange={() => handleLocationChange(location)}
                        />
                        <label 
                          htmlFor={`location_${location.id}`} 
                          className={`list-group-item d-flex align-items-center p-3 ${selectedLocation && selectedLocation.id === location.id ? 'selected' : ''}`}
                        >
                          <i className={`flag flag-${location.flag}`} style={{ marginRight: '10px' }}></i>
                          <div>
                            <div className="fw-bold">{location.name}</div>
                            {location.stock && (
                              <div className="text-muted small">
                                <span style={{ color: location.stockcolor }}>{location.stock}</span>
                              </div>
                            )}
                          </div>
                        </label>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </Col>

            {/* IPv4 Subnet Selection */}
            <Col md={12} lg={2}>
              <Card className="h-100 border">
                <div className="card-header bg-light">
                  <h5 className="mb-0">
                    <i className="fa fa-sitemap text-primary me-2" style={{paddingRight: '0.5rem'}}></i>
                    IPv4 Subnet
                  </h5>
                </div>
                <CardBody className="p-0">
                  <div className="better-radio-group">
                    {subnetOptions.map(subnet => (
                      <div key={subnet.id} className="position-relative">
                        <input
                          type="radio"
                          id={`subnet_${subnet.id}`}
                          name="subnet"
                          className="better-radio"
                          checked={selectedSubnet && selectedSubnet.id === subnet.id}
                          onChange={() => handleSubnetChange(subnet)}
                        />
                        <label 
                          htmlFor={`subnet_${subnet.id}`} 
                          className={`list-group-item d-flex flex-column p-3 ${selectedSubnet && selectedSubnet.id === subnet.id ? 'selected' : ''}`}
                        >
                          <div className="fw-bold">{subnet.name}</div>
                          <div className="text-success">
                            {subnet.price > 0 ? `+€${subnet.price}` : 'Included'}
                          </div>
                        </label>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </Col>

            {/* Contract Term Selection */}
            <Col md={12} lg={2}>
              <Card className="h-100 border">
                <div className="card-header bg-light">
                  <h5 className="mb-0">
                    <i className="fa fa-file-text text-primary me-2" style={{paddingRight: '0.5rem'}}></i>
                    Contract Term
                  </h5>
                </div>
                <CardBody className="p-0">
                  <div className="better-radio-group">
                    {contractOptions.map(contract => (
                      <div key={contract.id} className="position-relative">
                        <input
                          type="radio"
                          id={`contract_${contract.id}`}
                          name="contract"
                          className="better-radio"
                          checked={selectedContract && selectedContract.id === contract.id}
                          onChange={() => handleContractChange(contract)}
                        />
                        <label 
                          htmlFor={`contract_${contract.id}`} 
                          className={`list-group-item d-flex flex-column p-3 ${selectedContract && selectedContract.id === contract.id ? 'selected' : ''}`}
                        >
                          <div className="fw-bold">{contract.name}</div>
                          <div className="text-success">
                            {contract.price > 0 ? `+€${contract.price}` : 'Included'}
                          </div>
                        </label>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </Col>
          </Row>

          {/* Order Summary and Action Buttons */}
          <Card className="border-0 bg-light shadow-sm p-4">
            <Row className="align-items-center">
              <Col xs={12} md={6}>
                <h3 className="mb-0">Total Price: <span className="text-success">€{totalPrice}/mo</span></h3>
                <p className="text-muted mb-0">Delivery: {deliveryTime}</p>
              </Col>
              <Col xs={12} md={6} className="text-md-end mt-3 mt-md-0 d-flex justify-content-end">
                <Button className="btn btn-success" size="lg" onClick={handleOrderClick} disabled={loading}>
                  {loading ? (
                    <>
                      <Spinner size="sm" className="me-2" />
                      
                    </>
                  ) : (
                    <>
                      <i className="fa fa-check-circle me-2" style={{paddingRight: '0.5rem'}}></i>
                      Order Now
                    </>
                  )}
                </Button>
              </Col>
            </Row>
          </Card>
        </CardBody>
      </Card>

      {/* Order Confirmation Modal */}
      <Modal 
        isOpen={showConfirmModal} 
        toggle={toggleConfirmModal}
        className="order-summary-modal"
        backdropClassName="fixed-backdrop"
        backdrop={true}
        zIndex={9999}
        size="lg"
      >
        <ModalHeader toggle={toggleConfirmModal}>Order Summary</ModalHeader>
        <ModalBody>
          {error && (
            <Alert color="danger" className="mb-4">
              <i className="fa fa-exclamation-circle me-2"></i>
              <strong>Error:</strong> {error}
            </Alert>
          )}
          
          {selectedRack && selectedPower && selectedBandwidth && selectedLocation && selectedSubnet && selectedContract && (
            <>
              <h5 className="mb-3">Selected Configuration</h5>
              <div className="table-responsive mb-4">
                <table className="table card-table table-vcenter text-nowrap">
                  <tbody>
                    <tr>
                      <td><b>Rack Size</b></td>
                      <td>{selectedRack.size}</td>
                    </tr>
                    <tr>
                      <td><b>Power</b></td>
                      <td>{selectedPower.name}</td>
                    </tr>
                    <tr>
                      <td><b>Bandwidth</b></td>
                      <td>{selectedBandwidth.name}</td>
                    </tr>
                    <tr>
                      <td><b>Location</b></td>
                      <td><i className={`flag flag-${selectedLocation.flag}`}></i> {selectedLocation.name}</td>
                    </tr>
                    <tr>
                      <td><b>IP Subnet</b></td>
                      <td>
                        <select 
                          className="form-select form-control" 
                          value={tempSubnet ? tempSubnet.id : selectedSubnet.id}
                          onChange={(e) => {
                            const subnet = subnetOptions.find(s => s.id === e.target.value);
                            handleTempSubnetChange(subnet);
                          }}
                        >
                          {subnetOptions.map(subnet => (
                            <option key={subnet.id} value={subnet.id}>
                              {subnet.name} {subnet.price > 0 ? `(+€${subnet.price})` : '(Included)'}
                            </option>
                          ))}
                        </select>
                      </td>
                    </tr>
                    <tr>
                      <td><b>Contract Term</b></td>
                      <td>
                        <select 
                          className="form-select form-control"
                          value={tempContract ? tempContract.id : selectedContract.id}
                          onChange={(e) => {
                            const contract = contractOptions.find(c => c.id === e.target.value);
                            handleTempContractChange(contract);
                          }}
                        >
                          {contractOptions.map(contract => (
                            <option key={contract.id} value={contract.id}>
                              {contract.name} {contract.price > 0 ? `(+€${contract.price})` : '(Included)'}
                            </option>
                          ))}
                        </select>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              
              <div className="card border-0 bg-light shadow-sm p-3 mb-3">
                <div className="d-flex justify-content-between align-items-center">
                  <div>
                    <h4 className="mb-0">Total Price: <span className="text-success">€{modalTotalPrice}/mo</span></h4>
                    <p className="text-muted mb-0">Delivery: {deliveryTime}</p>
                  </div>
                </div>
              </div>
            </>
          )}
        </ModalBody>
        <ModalFooter className="d-flex flex-column p-3">
          {/* Top row with terms of service */}
          <div className="w-100 mb-3">
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <input
                type="checkbox"
                id="agreeTerms"
                checked={termsAccepted}
                onChange={() => setTermsAccepted(!termsAccepted)}
                style={{ 
                  marginRight: '5px',
                  marginLeft: '0',
                  marginTop: '0',
                  marginBottom: '0',
                  padding: '0',
                  position: 'relative',
                  float: 'none'
                }}
                required
              />
              I accept the <a href="#" target="_blank" style={{ marginLeft: '3px', color: '#1e88e5' }}>Terms of Service</a>
            </div>
          </div>
          
          {/* Bottom row with buttons */}
          <div className="d-flex justify-content-between w-100">
            <Button color="secondary" onClick={toggleConfirmModal} disabled={loading}>Cancel</Button>
            <Button 
              color="success" 
              onClick={handleOrder} 
              disabled={!termsAccepted || loading}
            >
              {loading ? (
                <>
                  <Spinner size="sm" className="me-2" />
                  
                </>
              ) : (
                'Place Order'
              )}
            </Button>
          </div>
        </ModalFooter>
      </Modal>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={showPaymentModal}
        toggle={togglePaymentModal}
        totalAmount={totalPrice}
        onComplete={handlePaymentComplete}
        orderId={orderId}
      />
    </Container>
  );
};

export default ColocationOrder;