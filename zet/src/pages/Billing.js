import React, { useEffect, useState } from "react"
import { Outlet, Link, useParams, useLocation, useNavigate } from "react-router-dom";
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Toast } from 'primereact/toast';
import { useRef } from 'react';
import 'primereact/resources/themes/mdc-light-indigo/theme.css';
import "primereact/resources/primereact.min.css";    
import 'primeflex/primeflex.css';
import DiscountAlert from "../components/DiscountAlert";

const Billing = () => {
  let navigate = useNavigate(); 
  const routeChange = (path) =>{ 
    navigate(path);
  }
  const toast = useRef(null);
  const path = useLocation();
  const params = useParams();
  const [bills, setBill] = useState([]);
  const [history, setHistory] = useState([]);
  const [cards, setCard] = useState([]);
  const [loading, setLoading] = useState(true);
  const [creditLoading, setCreditLoading] = useState(true);
  const [billsLoading, setBillsLoading] = useState(true);
  const [historyLoading, setHistoryLoading] = useState(true);
  const [cardsLoading, setCardsLoading] = useState(true);
  const [payingInvoice, setPayingInvoice] = useState(false);
  
  // Add access control state
  const [creditAccess, setCreditAccess] = useState(null); // null = loading, true = access, false = denied
  const [invoicesAccess, setInvoicesAccess] = useState(null);
  const [billingAccess, setBillingAccess] = useState(null); // For history and payment methods
  const [creditAccessError, setCreditAccessError] = useState('');
  const [invoicesAccessError, setInvoicesAccessError] = useState('');
  const [billingAccessError, setBillingAccessError] = useState('');
  
  const [creditStats, setCreditStats] = useState({
    available_credit: 0,
    free_credit: 0,
    pending_credit: 0
  });
  const [billingStats, setBillingStats] = useState({
    monthly_spending: 0,
    yearly_spending: 0,
    last_payment: 0,
    active_services: 0,
    unpaid_amount: 0
  });
  const [paymentMethod, setPaymentMethod] = useState('cc');
  const [creditAmount, setCreditAmount] = useState('');
  const [addingCredit, setAddingCredit] = useState(false);
  const [showAddCardModal, setShowAddCardModal] = useState(false);
  const [newCard, setNewCard] = useState({
    cardNumber: '',
    cardName: '',
    expiryMonth: '',
    expiryYear: '',
    cvv: ''
  });
  const [addingCard, setAddingCard] = useState(false);

  // Function to get token from session storage
  function getToken() {
    return sessionStorage.getItem('token');
  }

  // No Access Component
  const NoAccessMessage = ({ message, section }) => (
    <div className="text-center p-5">
      <i className="fa fa-lock fa-4x text-danger mb-4"></i>
      <h3 className="text-danger mb-3">Access Denied</h3>
      <p className="text-muted mb-4" style={{fontSize: '1.1rem'}}>
        {message || `You do not have permission to access ${section}.`}
      </p>
      <p className="text-muted">
        Please contact your administrator if you believe this is an error.
      </p>
      <div className="mt-4">
        <button 
          className="btn btn-secondary me-2" 
          onClick={() => window.location.reload()}
        >
          <i className="fa fa-refresh me-1"></i> Refresh Page
        </button>
      </div>
    </div>
  );
  
  // Function to fetch billing data (invoices)
  const fetchBillingData = () => {
    setBillsLoading(true);
    const token = getToken();
    
    fetch("/api.php?f=bills", {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ token })
    })
      .then(response => {
        console.log("Bills Response status:", response.status);
        
        // Check for 403 status code BEFORE trying to parse JSON
        if (response.status === 403) {
          console.log("403 detected - setting no access for invoices");
          setInvoicesAccess(false);
          setInvoicesAccessError('You do not have permission to access invoices');
          setBillsLoading(false);
          throw new Error('Access denied'); // Stop the promise chain
        }
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return response.json();
      })
      .then(data => {
        console.log("Bills Response Data:", data);
        
        if (data.error) {
          if (data.error === 5) {
            alert('ERROR: Your login session has timed out');
            window.location.reload(false);
          } else if (data.message && (data.message.includes('permission') || data.message.includes('Access denied'))) {
            setInvoicesAccess(false);
            setInvoicesAccessError(data.message || 'Access denied');
            setBillsLoading(false);
            return;
          } else {
            toast.current.show({
              severity: 'error',
              summary: 'Error',
              detail: `Error fetching invoices: ${data.error}`,
              life: 3000
            });
          }
        } else {
          setBill(Array.isArray(data) ? data : []);
          setInvoicesAccess(true);
        }
        setBillsLoading(false);
      })
      .catch(error => {
        console.error("Error fetching billing data:", error);
        if (error.message !== 'Access denied') {
          setInvoicesAccess(true); // Keep accessible for network errors
        }
        setBillsLoading(false);
      });
  }

  const toggleAddCardModal = () => {
    setShowAddCardModal(!showAddCardModal);
    // Reset form when opening
    if (!showAddCardModal) {
      setNewCard({
        cardNumber: '',
        cardName: '',
        expiryMonth: '',
        expiryYear: '',
        cvv: ''
      });
    }
  };

  // Add this function to handle input changes
  const handleCardInputChange = (e) => {
    const { name, value } = e.target;
    
    if (name === 'cardNumber') {
      // Format card number with spaces
      const cleanValue = value.replace(/\D/g, '');
      let formattedValue = '';
      
      for (let i = 0; i < cleanValue.length; i++) {
        if (i > 0 && i % 4 === 0) {
          formattedValue += ' ';
        }
        formattedValue += cleanValue[i];
      }
      
      setNewCard(prev => ({
        ...prev,
        [name]: formattedValue
      }));
    } else {
      setNewCard(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Function to fetch credit data
  const fetchCreditData = () => {
    setCreditLoading(true);
    const token = getToken();
    
    fetch("/api.php?f=credit", {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ token })
    })
      .then(response => {
        console.log("Credit Response status:", response.status);
        
        // Check for 403 status code BEFORE trying to parse JSON
        if (response.status === 403) {
          console.log("403 detected - setting no access for credit");
          setCreditAccess(false);
          setCreditAccessError('You do not have permission to access credit information');
          setCreditLoading(false);
          throw new Error('Access denied');
        }
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return response.json();
      })
      .then(data => {
        console.log("Credit Response Data:", data);
        
        if (data.error) {
          if (data.error === 5) {
            alert('ERROR: Your login session has timed out');
            window.location.reload(false);
          } else if (data.message && (data.message.includes('permission') || data.message.includes('Access denied'))) {
            setCreditAccess(false);
            setCreditAccessError(data.message || 'Access denied');
            setCreditLoading(false);
            return;
          } else {
            toast.current.show({
              severity: 'error',
              summary: 'Error',
              detail: `Error fetching credit info: ${data.error}`,
              life: 3000
            });
          }
        } else {
          setCreditStats(data);
          setCreditAccess(true);
        }
        setCreditLoading(false);
      })
      .catch(error => {
        console.error('Error fetching credit info:', error);
        if (error.message !== 'Access denied') {
          setCreditAccess(true); // Keep accessible for network errors
          toast.current.show({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to fetch credit information. Please try again.',
            life: 3000
          });
        }
        setCreditLoading(false);
      });
  }

  // Add this function to handle form submission
  const handleAddCard = async (e) => {
    e.preventDefault();
    
    // Basic validation
    if (!newCard.cardNumber.trim() || !newCard.cardName.trim() || 
        !newCard.expiryMonth || !newCard.expiryYear || !newCard.cvv.trim()) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Please fill in all required fields',
        life: 3000
      });
      return;
    }
    
    setAddingCard(true);
    
    try {
      const token = getToken();
      
      // Clean card number (remove spaces)
      const cardNumberClean = newCard.cardNumber.replace(/\s+/g, '');
      
      // Create MySQL date format (YYYY-MM-DD) from expiry month/year
      // Add leading zeros to month if needed
      const month = newCard.expiryMonth.padStart(2, '0');
      // Convert 2-digit year to 4-digit year if needed
      const year = newCard.expiryYear.length === 2 ? `20${newCard.expiryYear}` : newCard.expiryYear;
      // Format as YYYY-MM-01 (first day of expiry month)
      const expiryDate = `${year}-${month}-01`;
      
      console.log(`Converting expiry date: Month=${month}, Year=${year} to MySQL format: ${expiryDate}`);
      
      // Send data to the fixed API endpoint
      const response = await fetch("/api.php?f=add_card_with_cvv", { // Updated endpoint name for clarity
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token,
          card_number: cardNumberClean,
          card_name: newCard.cardName,
          expiry_date: expiryDate,
          cvv: newCard.cvv,
          store_cvv: true // Add flag to store CVV in database
        })
      });
      
      // Handle 403 response
      if (response.status === 403) {
        toast.current.show({
          severity: 'error',
          summary: 'Access Denied',
          detail: 'You do not have permission to add payment methods',
          life: 3000
        });
        setAddingCard(false);
        return;
      }
      
      const data = await response.json();
      
      if (data.error) {
        throw new Error(data.error);
      } else {
        // Determine card type for display
        let cardType = 'Card';
        if (cardNumberClean.startsWith('4')) {
          cardType = 'Visa';
        } else if (cardNumberClean.startsWith('5')) {
          cardType = 'Mastercard';
        } else if (cardNumberClean.startsWith('3')) {
          cardType = 'Amex';
        } else if (cardNumberClean.startsWith('6')) {
          cardType = 'Discover';
        }
        
        // Add card to UI
        const newCardData = {
          id: data.card_id || new Date().getTime(),
          type: cardType,
          card_number: cardNumberClean,
          // Format for display as MM/YY
          expiration: `${month}/${newCard.expiryYear.slice(-2)}`,
          cvv: newCard.cvv
        };
        
        setCard(prevCards => [...prevCards, newCardData]);
        
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: 'Payment method added successfully',
          life: 3000
        });
        
        setShowAddCardModal(false);
      }
    } catch (error) {
      console.error('Error adding card:', error);
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: `Card addition failed: ${error.message}`,
        life: 5000
      });
    } finally {
      setAddingCard(false);
    }
  };

  // Function to fetch billing statistics
  const fetchBillingStats = () => {
    setLoading(true);
    const token = getToken();
    
    fetch("/api.php?f=billing_stats", {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ token })
    })
      .then(response => {
        // Handle 403 response
        if (response.status === 403) {
          console.log("403 detected for billing stats - user may not have billing access");
          // Don't block the whole page for billing stats failure
          setLoading(false);
          return null;
        }
        
        return response.json();
      })
      .then(data => {
        if (!data) return; // Handle 403 case
        
        if (data.error) {
          if (data.error === 5) {
            alert('ERROR: Your login session has timed out');
            window.location.reload(false);
          } 
        } else {
          setBillingStats(data);
        }
        setLoading(false);
      })
      .catch(error => {
        console.error('Error fetching billing stats:', error);
        setLoading(false);
      });
  }
  
  // Function to fetch payment history
  const fetchHistoryData = () => {
    setHistoryLoading(true);
    const token = getToken();
    
    fetch("/api.php?f=history", {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ token })
    })
      .then(response => {
        console.log("History Response status:", response.status);
        
        // Check for 403 status code BEFORE trying to parse JSON
        if (response.status === 403) {
          console.log("403 detected - setting no access for payment history");
          setBillingAccess(false);
          setBillingAccessError('You do not have permission to access payment history');
          setHistoryLoading(false);
          throw new Error('Access denied');
        }
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return response.json();
      })
      .then(data => {
        console.log("History Response Data:", data);
        
        if (data.error) {
          if (data.error === 5) {
            alert('ERROR: Your login session has timed out');
            window.location.reload(false);
          } else if (data.message && (data.message.includes('permission') || data.message.includes('Access denied'))) {
            setBillingAccess(false);
            setBillingAccessError(data.message || 'Access denied');
            setHistoryLoading(false);
            return;
          } else {
            toast.current.show({
              severity: 'error',
              summary: 'Error',
              detail: `Error fetching payment history: ${data.error}`,
              life: 3000
            });
          }
        } else {
          // Ensure data is always treated as an array
          setHistory(Array.isArray(data) ? data : []);
          setBillingAccess(true);
        }
        setHistoryLoading(false);
      })
      .catch(error => {
        console.error("Error fetching history data:", error);
        if (error.message !== 'Access denied') {
          setBillingAccess(true); // Keep accessible for network errors
        }
        setHistoryLoading(false);
      });
  }
  
  // Function to fetch saved payment methods
  const fetchCardData = () => {
    setCardsLoading(true);
    const token = getToken();
    
    fetch("/api.php?f=cards", {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ 
        token,
        include_cvv: true // Request CVV data from the API
      })
    })
      .then(response => {
        console.log("Cards Response status:", response.status);
        
        // Check for 403 status code BEFORE trying to parse JSON
        if (response.status === 403) {
          console.log("403 detected - setting no access for payment methods");
          setBillingAccess(false);
          setBillingAccessError('You do not have permission to access payment methods');
          setCardsLoading(false);
          throw new Error('Access denied');
        }
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return response.json();
      })
      .then(data => {
        console.log("Cards Response Data:", data);
        
        if (data.error) {
          if (data.error === 5) {
            alert('ERROR: Your login session has timed out');
            window.location.reload(false);
          } else if (data.message && (data.message.includes('permission') || data.message.includes('Access denied'))) {
            setBillingAccess(false);
            setBillingAccessError(data.message || 'Access denied');
            setCardsLoading(false);
            return;
          } else if (data.error === 'No cards found') {
            // Specifically handle the case of no saved cards
            setCard([]);
          } 
        } else {
          // Process the card data to ensure proper format
          const processedCards = Array.isArray(data) ? data.map(card => {
            // Ensure card_number is a string and has at least 4 digits
            if (typeof card.card_number === 'string' && card.card_number.length >= 4) {
              return {
                ...card,
                // Ensure CVV exists, use empty string if not provided
                cvv: card.cvv || ''
              };
            }
            return card;
          }) : [];
          
          setCard(processedCards);
          setBillingAccess(true);
        }
        setCardsLoading(false);
      })
      .catch(error => {
        console.error("Error fetching card data:", error);
        if (error.message !== 'Access denied') {
          setBillingAccess(true); // Keep accessible for network errors
        }
        setCardsLoading(false);
      });
  }

  // Function to add credit
  const handleAddCredit = () => {
    const numericCreditAmount = parseFloat(creditAmount);
    
    if (isNaN(numericCreditAmount) || numericCreditAmount < 300) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Minimum amount is €300',
        life: 3000
      });
      return;
    }
    
    setAddingCredit(true);
    
    const token = sessionStorage.getItem('token');
    
    fetch("/api.php?f=generate_credit_invoice", {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token: token,
        payment_method: paymentMethod,
        amount: numericCreditAmount
      })
    })
    .then(response => {
      // Handle 403 response
      if (response.status === 403) {
        toast.current.show({
          severity: 'error',
          summary: 'Access Denied',
          detail: 'You do not have permission to generate credit invoices',
          life: 3000
        });
        setAddingCredit(false);
        return null;
      }
      
      // Important: Parse JSON and check for errors
      return response.json().then(data => {
        if (!response.ok) {
          throw new Error(data.details || 'Unknown error occurred');
        }
        return data;
      });
    })
    .then(data => {
      if (!data) return; // Handle 403 case
      
      if (data.error) {
        throw new Error(data.details || data.error);
      }
      
      toast.current.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Credit purchase invoice generated successfully!',
        life: 3000
      });
      
      // Navigate to invoice or refresh credit data
      if (data.invoice_id) {
        navigate(`/billing/invoices/${data.invoice_id}`);
      }
      
      // Optional: Show bonus information
      if (data.bonus) {
        toast.current.show({
          severity: 'info',
          summary: 'Bonus Credited',
          detail: `You received a bonus of €${data.bonus}!`,
          life: 3000
        });
      }
    })
    .catch(error => {
      console.error('Credit Invoice Error:', error);
      
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: error.message || 'Failed to generate credit invoice',
        life: 3000
      });
    })
    .finally(() => {
      setAddingCredit(false);
    });
  };
  
  // Initial data fetch
  useEffect(() => {
    fetchBillingData();
    fetchHistoryData();
    fetchCardData();
    fetchCreditData();
    fetchBillingStats();
  }, []);
  
  // Re-fetch billing data when path changes
  useEffect(() => {
    const pathPart = splitLastOccurrence(path.pathname, '/');
    
    if(pathPart === 'invoices' || path.pathname === '/billing') {
      fetchBillingData();
    }
    if(pathPart === 'history') {
      fetchHistoryData();
    }
    if(pathPart === 'methods') {
      fetchCardData();
    }
    if(pathPart === 'credit' || path.pathname === '/billing') {
      fetchCreditData();
    }
  }, [path.pathname]);

  // Helper function to get the last part of the URL path
  function splitLastOccurrence(str, substring) {
    const lastIndex = str.lastIndexOf(substring);
    const before = str.slice(0, lastIndex);
    const after = str.slice(lastIndex + 1);
    return after;
  }

  // Add these validation functions
  const validateCardNumber = (number) => {
    // Remove spaces and non-digit characters
    const cleanNumber = number.replace(/\D/g, '');
    // Check if the number has valid length
    return cleanNumber.length >= 13 && cleanNumber.length <= 19;
  };

  const validateExpiryDate = (month, year) => {
    if (!month || !year) return false;
    
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-indexed
    
    const expiryMonth = parseInt(month, 10);
    const expiryYear = parseInt(year, 10);
    
    // Check if date is in the past
    if (expiryYear < currentYear || (expiryYear === currentYear && expiryMonth < currentMonth)) {
      return false;
    }
    
    return expiryMonth >= 1 && expiryMonth <= 12;
  };

  // Custom formatter for card types
  const cardType = (cards) => {
    let iconClass = "fa fa-credit-card"; // Default icon
    
    switch(cards.type.toLowerCase()) {
      case 'visa':
        iconClass = "fa fa-cc-visa";
        break;
      case 'mastercard':
        iconClass = "fa fa-cc-mastercard";
        break;
      case 'amex':
        iconClass = "fa fa-cc-amex";
        break;
      case 'discover':
        iconClass = "fa fa-cc-discover";
        break;
      default:
        iconClass = "fa fa-credit-card";
    }
    
    return (
      <div>
        <i style={{marginBottom:"-3px"}} className={iconClass}></i> &nbsp;{cards.type}
      </div>
    );
  };
  
  // Custom formatter for invoice status
  const statusFormatter = (rowData) => {
    if (rowData.status === 'Paid') {
      return <span className="badge bg-success">Paid</span>;
    } else if (rowData.status === 'Unpaid') {
      return <span className="badge bg-danger">Unpaid</span>;
    } else if (rowData.status === 'Overdue') {
      return <span className="badge bg-warning">Overdue</span>;
    } else {
      return <span className="badge bg-secondary">{rowData.status}</span>;
    }
  };
  
  // Custom formatter for amount with Euro symbol
  const amountFormatter = (rowData) => {
    return <span className="fw-bold">€{rowData.total}</span>;
  };
  
  // Function to handle paying all invoices
  const handlePayAllInvoices = () => {
    // This would need to be implemented based on your payment flow
    toast.current.show({
      severity: 'info',
      summary: 'Pay Invoices',
      detail: 'Payment processing would be implemented here',
      life: 3000
    });
  };
  
  const handlePayInvoice = (invoiceId) => {
    setPayingInvoice(true);
    const token = getToken();
    
    fetch("/api.php?f=process_paid_invoice", {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ 
        token,
        invoice_id: invoiceId
      })
    })
      .then(response => {
        return response.json();
      })
      .then(data => {
        if (data.error) {
          if (data.error === 5) {
            alert('ERROR: Your login session has timed out');
            window.location.reload(false);
          } else {
            toast.current.show({
              severity: 'error',
              summary: 'Error',
              detail: data.error,
              life: 3000
            });
          }
        } else {
          toast.current.show({
            severity: 'success',
            summary: 'Success',
            detail: 'Invoice paid successfully! Credit has been added to your account.',
            life: 3000
          });
          
          // Refresh data
          fetchCreditData();
          
          // If this is a credit purchase invoice, navigate back to credit tab
          if (data.invoice_type === 'Credit Purchase') {
            navigate('/billing/credit');
          }
        }
        setPayingInvoice(false);
      })
      .catch(error => {
        console.error('Error paying invoice:', error);
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to process payment. Please try again.',
          life: 3000
        });
        setPayingInvoice(false);
      });
  };
  
  // Add a Pay button component for credit purchase invoices
  const CreditPurchaseInvoicePayButton = ({ invoice }) => {
    if (invoice.status === 'Paid' || invoice.type !== 'Credit Purchase') {
      return null;
    }
    
    return (
      <button 
        className="btn btn-success"
        onClick={() => handlePayInvoice(invoice.id)}
        disabled={payingInvoice}
      >
        {payingInvoice ? (
          <>
            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
            Processing Payment
          </>
        ) : (
          <>
            <i className="fa fa-credit-card me-1"></i> Pay Now & Add Credit
          </>
        )}
      </button>
    );
  };

  const CreditPurchaseNote = ({ invoice }) => {
    if (invoice.type !== 'Credit Purchase') {
      return null;
    }
    
    if (invoice.status === 'Paid') {
      return (
        <div className="alert alert-success" role="alert">
          <i className="fa fa-check-circle me-2"></i>
          <strong>Credit Added!</strong> Your account has been credited with €{invoice.total}.
          {invoice.bonus > 0 && (
            <span> Plus a bonus of €{invoice.bonus}!</span>
          )}
        </div>
      );
    }
    
    return (
      <div className="alert alert-info" role="alert">
        <i className="fa fa-info-circle me-2"></i>
        <strong>Credit Purchase:</strong> Once this invoice is paid, €{invoice.total} will be added to your account balance.
        {parseFloat(invoice.total) >= 500 && (
          <span> Plus you'll receive a 5% bonus credit!</span>
        )}
      </div>
    );
  }

  // Function to remove a payment method
  const handleRemovePaymentMethod = (id) => {
    // Show confirmation dialog
    if (!window.confirm("Are you sure you want to remove this payment method?")) {
      return;
    }
    
    // Set loading state
    setCardsLoading(true);
    const token = getToken();
    
    // Call API to delete the payment method
    fetch("/api.php?f=delete_payment_method", {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ 
        token,
        payment_method_id: id
      })
    })
      .then(response => {
        // Handle 403 response
        if (response.status === 403) {
          toast.current.show({
            severity: 'error',
            summary: 'Access Denied',
            detail: 'You do not have permission to delete payment methods',
            life: 3000
          });
          setCardsLoading(false);
          return null;
        }
        
        return response.json();
      })
      .then(data => {
        if (!data) return; // Handle 403 case
        
        if (data.error) {
          if (data.error === 5) {
            alert('ERROR: Your login session has timed out');
            window.location.reload(false);
          } else {
            toast.current.show({
              severity: 'error',
              summary: 'Error',
              detail: `Error removing payment method: ${data.error}`,
              life: 3000
            });
          }
        } else {
          // Update the cards state to remove the deleted card
          setCard(prevCards => prevCards.filter(card => card.id !== id));
          
          toast.current.show({
            severity: 'success',
            summary: 'Success',
            detail: 'Payment method removed successfully',
            life: 3000
          });
        }
        setCardsLoading(false);
      })
      .catch(error => {
        console.error('Error removing payment method:', error);
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to remove payment method. Please try again.',
          life: 3000
        });
        setCardsLoading(false);
      });
  };

  return (
    <>
      <Toast ref={toast} />
      <div className="page-header">
        <div className="page-leftheader">
          <ol className="breadcrumb">
            <li className="breadcrumb-item1"><Link to="/">Home</Link></li>
            <li className="breadcrumb-item1">Billing</li>
          </ol>
        </div>
        <div className="page-rightheader ml-auto">
          <div className="dropdown">
            <a href="#" className="nav-link pr-0 leading-none" data-toggle="dropdown">
              <button className="btn btn-success">New Service</button>
            </a>
            <div className="dropdown-menu">
              <Link className="dropdown-item d-flex" to="/dedicatedorder">
                <div className="mt-1">Dedicated Server</div>
              </Link>
              <Link className="dropdown-item d-flex" to="/cloudorder">
                <div className="mt-1">Cloud Server</div>
              </Link>
            </div>
          </div>
        </div>
      </div>
      
      <div className="row">
        <div className="col-12">
          <DiscountAlert />

          {showAddCardModal && (
            <div className="modal d-block" tabIndex="-1" role="dialog" style={{backgroundColor: 'rgba(0,0,0,0.5)'}}>
              <div className="modal-dialog" role="document">
                <div className="modal-content">
                  <div className="modal-header">
                    <h5 className="modal-title">Add Payment Method</h5>
                    <button type="button" className="close" onClick={toggleAddCardModal}>
                      <span aria-hidden="true">&times;</span>
                    </button>
                  </div>
                  <form onSubmit={handleAddCard}>
                    <div className="modal-body">
                      <div className="form-group mb-3">
                        <label>Card Number</label>
                        <input
                          type="text"
                          className="form-control"
                          name="cardNumber"
                          placeholder="Card Number"
                          value={newCard.cardNumber}
                          onChange={handleCardInputChange}
                          maxLength="19"
                          required
                        />
                      </div>
                      
                      <div className="form-group mb-3">
                        <label>Cardholder Name</label>
                        <input
                          type="text"
                          className="form-control"
                          name="cardName"
                          placeholder="Name on Card"
                          value={newCard.cardName}
                          onChange={handleCardInputChange}
                          required
                        />
                      </div>
                      
                      <div className="row">
                        <div className="col-md-7">
                          <div className="form-group mb-3">
                            <label>Expiration Date</label>
                            <div className="d-flex">
                              <select
                                className="form-control me-2"
                                name="expiryMonth"
                                value={newCard.expiryMonth}
                                onChange={handleCardInputChange}
                                required
                              >
                                <option value="">MM</option>
                                <option value="01">01</option>
                                <option value="02">02</option>
                                <option value="03">03</option>
                                <option value="04">04</option>
                                <option value="05">05</option>
                                <option value="06">06</option>
                                <option value="07">07</option>
                                <option value="08">08</option>
                                <option value="09">09</option>
                                <option value="10">10</option>
                                <option value="11">11</option>
                                <option value="12">12</option>
                              </select>
                              <select
                                className="form-control"
                                name="expiryYear"
                                value={newCard.expiryYear}
                                onChange={handleCardInputChange}
                                required
                              >
                                <option value="">YY</option>
                                {Array.from({ length: 10 }, (_, i) => {
                                  const year = (new Date().getFullYear() + i).toString().slice(-2);
                                  return (
                                    <option key={year} value={year}>
                                      {year}
                                    </option>
                                  );
                                })}
                              </select>
                            </div>
                          </div>
                        </div>
                        <div className="col-md-5">
                          <div className="form-group mb-3">
                            <label>CVV</label>
                            <input
                              type="text"
                              className="form-control"
                              name="cvv"
                              placeholder="CVV"
                              value={newCard.cvv}
                              onChange={handleCardInputChange}
                              maxLength="4"
                              required
                            />
                          </div>
                        </div>
                      </div>
                      
                      <div className="alert alert-info">
                        <i className="fa fa-info-circle me-2"></i>
                        Your card information is encrypted and securely stored according to PCI DSS standards.
                      </div>
                    </div>
                    
                    <div className="modal-footer">
                      <button 
                        type="button" 
                        className="btn btn-secondary" 
                        onClick={toggleAddCardModal}
                        disabled={addingCard}
                      >
                        Cancel
                      </button>
                      <button 
                        type="submit" 
                        className="btn btn-primary"
                        disabled={addingCard}
                      >
                        {addingCard ? (
                          <>
                            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                            Processing...
                          </>
                        ) : 'Add Card'}
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          )}

          <div className="card">
            <div className="card-header">
              <div className="card-title">Billing</div>
              <div className="card-options">
                <Link to="/billing/credit" className="btn btn-success btn-sm">Add Credit</Link>&nbsp;
              </div>
            </div>
            <div className="card-body">
              <div className="panel panel-primary">
                <div className="tab-menu-heading">
                  <div className="tabs-menu">
                    <ul className="nav panel-tabs">
                      <li>
                        <Link 
                          to="/billing/credit" 
                          className={(splitLastOccurrence(path.pathname, '/') === 'credit' || path.pathname === '/billing') ? "active" : ""}
                        >
                          <i className="si si-wallet me-1"></i> Credit
                        </Link>
                      </li>
                      <li>
                        <Link 
                          to="/billing/invoices" 
                          className={splitLastOccurrence(path.pathname, '/') === 'invoices' ? "active" : ""}
                        >
                          <i className="si si-doc me-1"></i> Invoices
                        </Link>
                      </li>
                      <li>
                        <Link 
                          to="/billing/history" 
                          className={splitLastOccurrence(path.pathname, '/') === 'history' ? "active" : ""}
                        >
                          <i className="si si-clock me-1"></i> Payments History
                        </Link>
                      </li>
                      <li>
                        <Link 
                          to="/billing/methods" 
                          className={splitLastOccurrence(path.pathname, '/') === 'methods' ? "active" : ""}
                        >
                          <i className="si si-credit-card me-1"></i> Payment Methods
                        </Link>
                      </li>
                    </ul>
                  </div>
                </div>
                
                <div className="panel-body tabs-menu-body">
                  <div className="tab-content">
                    {/* Credit Section */}
                    {(splitLastOccurrence(path.pathname, '/') === 'credit' || path.pathname === '/billing') && (
                      <div className="tab-pane active">
                        {creditAccess === null ? (
                          // Loading state
                          <div className="d-flex justify-content-center p-5">
                            <div className="spinner-border text-primary" role="status">
           
                            </div>
                          </div>
                        ) : creditAccess === false ? (
                          // No access
                          <NoAccessMessage message={creditAccessError} section="credit information" />
                        ) : (
                          // Has access - show credit content
                          <div className="row">
                            {/* Credit Status Card */}
                            <div className="col-xl-4 col-lg-6 col-md-12">
                              <div className="card">
                                <div className="card-header">
                                  <h3 className="card-title">
                                    <i className="si si-wallet text-primary me-2"></i> Credit Status
                                  </h3>
                                </div>
                                <div className="card-body text-center">
                                  {creditLoading ? (
                                    <div className="d-flex justify-content-center p-5">
                                      <div className="spinner-border text-primary" role="status">
                                      </div>
                                    </div>
                                  ) : (
                                    <>
                                      <div className="mb-3">
                                        <h1 className="display-4 font-weight-bold text-primary">
                                          €{(
                                            parseFloat(creditStats.available_credit || 0) + 
                                            parseFloat(creditStats.free_credit || 0)
                                          ).toFixed(2)}
                                        </h1>
                                        <p className="text-muted">Available Credit</p>
                                      </div>
                                      
                                      <ul className="list-group list-group-flush">
                                        <li className="list-group-item d-flex justify-content-between align-items-center">
                                          <span>Available Credit:</span>
                                          <span className="text-success fw-bold">
                                            €{parseFloat(creditStats.available_credit || 0).toFixed(2)}
                                          </span>
                                        </li>

                                        <li className="list-group-item d-flex justify-content-between align-items-center">
                                          <span>Pending Credit:</span>
                                          <span className="text-warning fw-bold">
                                            €{parseFloat(creditStats.pending_credit || 0).toFixed(2)}
                                          </span>
                                        </li>
                                      </ul>
                                    </>
                                  )}
                                </div>
                              </div>
                            </div>
                            
                            {/* Add Credit Card */}
                            <div className="col-xl-5 col-lg-6 col-md-12">
                              <div className="card">
                                <div className="card-header">
                                  <h3 className="card-title">
                                    <i className="si si-plus text-success me-2"></i> Add Credit
                                  </h3>
                                </div>
                                <div className="card-body">
                                  <form onSubmit={(e) => { e.preventDefault(); handleAddCredit(); }}>
                                    <div className="form-group mb-3">
                                      <label className="form-label">Payment Method</label>
                                      <select 
                                        className="form-control" 
                                        value={paymentMethod}
                                        onChange={(e) => setPaymentMethod(e.target.value)}
                                        disabled={addingCredit}
                                      >
                                        <option value="cc">Credit Card</option>
                                        <option value="crypto">Cryptocurrency</option>
                                        <option value="bank">Bank Transfer</option>
                                      </select>
                                    </div>
                                    
                                    <div className="form-group mb-4">
                                      <label className="form-label">
                                        Amount (€)
                                        <span className="text-danger">*</span>
                                      </label>
                                      <input 
                                        type="text" 
                                        className="form-control" 
                                        placeholder="300-500"
                                        required
                                        aria-required="true"
                                        minLength="3"
                                        value={creditAmount}
                                        onChange={(e) => setCreditAmount(e.target.value)}
                                        disabled={addingCredit}
                                      />
                                      <small className="form-text text-muted">
                                        Minimum amount: €300 
                                      </small>
                                    </div>
                                    
                                    <div className="form-footer text-end">
                                      <button 
                                        type="submit" 
                                        className="btn btn-success"
                                        disabled={addingCredit || !creditAmount || parseFloat(creditAmount) < 300}
                                      >
                                        {addingCredit ? (
                                          <>
                                            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                            Processing...
                                          </>
                                        ) : (
                                          <>
                                            <i className="fa fa-plus-circle me-1"></i> Add Credit
                                          </>
                                        )}
                                      </button>
                                    </div>
                                  </form>
                                </div>
                              </div>
                            </div>
                            
                            {/* Credit Benefits Card */}
                            <div className="col-xl-3 col-lg-12 col-md-12">
                              <div className="card">
                                <div className="card-header">
                                  <h3 className="card-title">
                                    <i className="si si-info text-info me-2"></i> Benefits
                                  </h3>
                                </div>
                                <div className="card-body">
                                  <ul className="list-unstyled">
                                    <li className="mb-2">
                                      <i className="fa fa-check-circle text-success me-2" style={{paddingRight: '0.5rem'}}></i>
                                      Automatic payments
                                    </li>
                                    <li className="mb-2">
                                      <i className="fa fa-check-circle text-success me-2" style={{paddingRight: '0.5rem'}}></i>
                                      No service interruptions
                                    </li>
                                    <li className="mb-2">
                                      <i className="fa fa-check-circle text-success me-2" style={{paddingRight: '0.5rem'}}></i>
                                      Consolidated billing
                                    </li>
                                  </ul>
                                  <div className="alert alert-info mt-3 mb-0">
                                    <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
                                    Add credit to your account to avoid service interruptions!
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                    
                    {/* Invoices Section */}
                    {splitLastOccurrence(path.pathname, '/') === 'invoices' && (
                      <div className="tab-pane active">
                        {invoicesAccess === null ? (
                          // Loading state
                          <div className="d-flex justify-content-center p-5">
                            <div className="spinner-border text-primary" role="status">
            
                            </div>
                          </div>
                        ) : invoicesAccess === false ? (
                          // No access
                          <NoAccessMessage message={invoicesAccessError} section="invoices" />
                        ) : (
                          // Has access - show invoices content
                          <div className="row">
                            {/* Billing Summary Card */}
                            <div className="col-xl-3 col-lg-6 col-md-12">
                              <div className="card">
                                <div className="card-body">
                                  <div className="d-flex align-items-center">
                                    <div className="me-3">
                                      <span className="avatar avatar-lg bg-danger-transparent">
                                        <i className="fa fa-exclamation-triangle text-danger"></i>
                                      </span>
                                    </div>
                                    <div>
                                      <h2 className="mb-0 font-weight-bold text-danger" style={{paddingLeft: '1.5rem'}}>
                                        €{parseFloat(billingStats.unpaid_amount).toFixed(2)}
                                      </h2>
                                      <span className="text-muted"style={{paddingLeft: '1.5rem'}}>Unpaid Invoices</span>
                                    </div>
                                  </div>
                                  <p className="mt-3 mb-4 text-muted">
                                    Please make sure to pay the outstanding amounts before the due dates to avoid suspension of your services.
                                  </p>
                                </div>
                              </div>
                            </div>
                            
                            {/* Invoices DataTable */}
                            <div className="col-xl-9 col-lg-6 col-md-12">
                              <div className="card smaller">
                                <div className="card-header">
                                  <h3 className="card-title">Invoices</h3>
                                  <div className="card-options">
                                    <button className="btn btn-outline-primary btn-sm">
                                      <i className="fa fa-download me-1"></i> Export
                                    </button>
                                  </div>
                                </div>
                                <div className="card-body p-0">
                                  <DataTable 
                                    value={bills} 
                                    onRowClick={(e) => { routeChange('/billing/invoices/'+e.data.id) }}  
                                    sortField="id" 
                                    sortOrder={-1} 
                                    paginator 
                                    rows={10} 
                                    rowsPerPageOptions={[5, 10, 25, 50]} 
                                    className="border-0"
                                    emptyMessage="No invoices found"
                                    loading={billsLoading}
                                  >
                                    <Column field="id" sortable header="Invoice #" headerStyle={{ fontWeight: 'bold' }} style={{cursor: 'pointer'}}></Column>
                                    <Column field="type" sortable header="Type" headerStyle={{ fontWeight: 'bold' }} style={{cursor: 'pointer'}}></Column>
                                    <Column field="total" body={amountFormatter} sortable header="Amount" headerStyle={{ fontWeight: 'bold' }} style={{cursor: 'pointer'}}></Column>
                                    <Column field="date" sortable header="Issued" headerStyle={{ fontWeight: 'bold' }} style={{cursor: 'pointer'}}></Column>
                                    <Column field="duedate" sortable header="Due Date" headerStyle={{ fontWeight: 'bold' }} style={{cursor: 'pointer'}}></Column>
                                    <Column field="status" body={statusFormatter} sortable header="Status" headerStyle={{ fontWeight: 'bold' }} style={{cursor: 'pointer'}}></Column>
                                  </DataTable>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                    
                    {/* Payment History Section */}
                    {splitLastOccurrence(path.pathname, '/') === 'history' && (
                      <div className="tab-pane active">
                        {billingAccess === null ? (
                          // Loading state
                          <div className="d-flex justify-content-center p-5">
                            <div className="spinner-border text-primary" role="status">
    
                            </div>
                          </div>
                        ) : billingAccess === false ? (
                          // No access
                          <NoAccessMessage message={billingAccessError} section="payment history" />
                        ) : (
                          // Has access - show payment history content
                          <div className="row">
                            {/* Payment Statistics */}
                            <div className="col-12 mb-4">
                              <div className="row">
                                <div className="col-xl-3 col-lg-6 col-md-6">
                                  <div className="card">
                                    <div className="card-body">
                                      <div className="d-flex">
                                        <div className="me-4">
                                          <span className="avatar avatar-lg bg-primary-transparent">
                                            <i className="si si-credit-card text-primary"></i>
                                          </span>
                                        </div>
                                        <div>
                                          <h6 className="mb-1" style={{paddingLeft: '1.5rem'}}>Monthly Spending</h6>
                                          <h2 className="mb-0 font-weight-bold" style={{paddingLeft: '1.5rem'}}>
                                            €{parseFloat(billingStats.monthly_spending).toFixed(2)}
                                          </h2>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                
                                <div className="col-xl-3 col-lg-6 col-md-6">
                                  <div className="card">
                                    <div className="card-body">
                                      <div className="d-flex">
                                        <div className="me-4">
                                          <span className="avatar avatar-lg bg-success-transparent">
                                            <i className="si si-graph text-success"></i>
                                          </span>
                                        </div>
                                        <div>
                                          <h6 className="mb-1" style={{paddingLeft: '1.5rem'}}>Yearly Spending</h6>
                                          <h2 className="mb-0 font-weight-bold" style={{paddingLeft: '1.5rem'}}>
                                            €{parseFloat(billingStats.yearly_spending).toFixed(2)}
                                          </h2>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                
                                <div className="col-xl-3 col-lg-6 col-md-6">
                                  <div className="card">
                                    <div className="card-body">
                                      <div className="d-flex">
                                        <div className="me-4">
                                          <span className="avatar avatar-lg bg-warning-transparent">
                                            <i className="si si-clock text-warning"></i>
                                          </span>
                                        </div>
                                        <div>
                                          <h6 className="mb-1" style={{paddingLeft: '1.5rem'}}>Last Payment</h6>
                                          <h2 className="mb-0 font-weight-bold" style={{paddingLeft: '1.5rem'}}>
                                            €{parseFloat(billingStats.last_payment).toFixed(2)}
                                          </h2>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                
                                <div className="col-xl-3 col-lg-6 col-md-6">
                                  <div className="card">
                                    <div className="card-body">
                                      <div className="d-flex">
                                        <div className="me-4">
                                          <span className="avatar avatar-lg bg-info-transparent">
                                            <i className="si si-layers text-info"></i>
                                          </span>
                                        </div>
                                        <div>
                                          <h6 className="mb-1" style={{paddingLeft: '1.5rem'}}>Active Services</h6>
                                          <h2 className="mb-0 font-weight-bold" style={{paddingLeft: '1.5rem'}}>
                                            {billingStats.active_services}
                                          </h2>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                            
                            {/* Payment History DataTable */}
                            <div className="col-12">
                              <div className="card">
                                <div className="card-header">
                                  <h3 className="card-title">
                                    <i className="si si-refresh text-primary me-2"></i>Payment History
                                  </h3>
                                  <div className="card-options">
                                    <button className="btn btn-outline-primary btn-sm">
                                      <i className="fa fa-download me-1"></i> Export
                                    </button>
                                  </div>
                                </div>
                                <div className="card-body p-0">
                                  <DataTable 
                                    value={history} 
                                    onRowClick={(e) => { routeChange('/billing/history/'+e.data.id) }}  
                                    sortField="id" 
                                    sortOrder={-1} 
                                    paginator 
                                    rows={10} 
                                    rowsPerPageOptions={[5, 10, 25, 50]} 
                                    className="border-0"
                                    emptyMessage="No payment history found"
                                    loading={historyLoading}
                                  >
                                    <Column field="id" sortable header="Transaction ID" headerStyle={{ fontWeight: 'bold' }} style={{cursor: 'pointer'}}></Column>
                                    <Column field="total" body={amountFormatter} sortable header="Amount" headerStyle={{ fontWeight: 'bold' }} style={{cursor: 'pointer'}}></Column>
                                    <Column field="date" sortable header="Date Paid" headerStyle={{ fontWeight: 'bold' }} style={{cursor: 'pointer'}}></Column>
                                    <Column field="method" sortable header="Payment Method" headerStyle={{ fontWeight: 'bold' }} style={{cursor: 'pointer'}}></Column>
                                    <Column field="status" body={statusFormatter} sortable header="Status" headerStyle={{ fontWeight: 'bold' }} style={{cursor: 'pointer'}}></Column>
                                  </DataTable>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                    
                    {/* Payment Methods Section */}
                    {splitLastOccurrence(path.pathname, '/') === 'methods' && (
                      <div className="tab-pane active">
                        {billingAccess === null ? (
                          // Loading state
                          <div className="d-flex justify-content-center p-5">
                            <div className="spinner-border text-primary" role="status">
                 
                            </div>
                          </div>
                        ) : billingAccess === false ? (
                          // No access
                          <NoAccessMessage message={billingAccessError} section="payment methods" />
                        ) : (
                          // Has access - show payment methods content
                          <div className="row">
                            <div className="col-md-4">
                              <div className="card">
                                <div className="card-body">
                                  <h4 className="mb-3">
                                    <i className="fa fa-plus-circle text-success me-2"style={{paddingRight: '0.5rem'}}></i>Add Payment Method
                                  </h4>
                                  <p className="text-muted mb-4" >
                                    Save your card(s) in your account and pay your invoices easier without manually reentering your card details. You can remove your card(s) anytime or change the payment preferences.
                                  </p>
                                  <button 
                                    className="btn btn-success w-100" 
                                    onClick={toggleAddCardModal}
                                  >
                                    <i className="fa fa-credit-card me-2"></i> Add Card
                                  </button>
                                </div>
                              </div>
                            </div>
                            
                            <div className="col-md-8">
                              <div className="card">
                                <div className="card-body">
                                  <h4 className="mb-3">
                                    <i className="fa fa-credit-card text-primary me-2"style={{paddingRight: '0.5rem'}}></i>Saved Payment Methods
                                  </h4>
                                  
                                  {cardsLoading ? (
                                    <div className="d-flex justify-content-center p-5">
                                      <div className="spinner-border text-primary" role="status">
                                      </div>
                                    </div>
                                  ) : (
                                    cards.length > 0 ? (
                                      <div className="table-responsive">
                                        <table className="table table-hover">
                                          <thead>
                                            <tr>
                                              <th>Last Digits</th>
                                              <th>CVV</th>
                                              <th>Type</th>
                                              <th>Expiration</th>
                                              <th>Actions</th>
                                            </tr>
                                          </thead>
                                          <tbody>
                                            {cards.map(card => (
                                              <tr key={card.id}>
                                                <td>
                                                  {typeof card.card_number === 'string' && card.card_number.length > 4 
                                                    ? `**** **** **** ${card.card_number.slice(-4)}` 
                                                    : card.card_number}
                                                </td>
                                                <td>{card.cvv}</td>
                                                <td>
                                                  {(() => {
                                                    let iconClass = "fa fa-cc-card"; // Default icon
                                                    
                                                    switch(card.type.toLowerCase()) {
                                                      case 'visa':
                                                        iconClass = "fa fa-cc-visa";
                                                        break;
                                                      case 'mastercard':
                                                        iconClass = "fa fa-cc-mastercard";
                                                        break;
                                                      case 'amex':
                                                        iconClass = "fa fa-cc-amex";
                                                        break;
                                                      case 'discover':
                                                        iconClass = "fa fa-cc-discover";
                                                        break;
                                                      default:
                                                        iconClass = "fa fa-credit-card";
                                                    }
                                                    
                                                    return <i className={iconClass}></i>;
                                                  })()} {card.type}
                                                </td>
                                                <td>{card.expiration}</td>
                                                <td>
                                                  <button 
                                                    className="btn btn-sm btn-danger"
                                                    onClick={() => handleRemovePaymentMethod(card.id)}
                                                  >
                                                    Remove
                                                  </button>
                                                </td>
                                              </tr>
                                            ))}
                                          </tbody>
                                        </table>
                                      </div>
                                    ) : (
                                      <div className="text-center p-4">
                                        <p className="text-muted">You don't have any saved payment methods yet.</p>
                                      </div>
                                    )
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Billing;