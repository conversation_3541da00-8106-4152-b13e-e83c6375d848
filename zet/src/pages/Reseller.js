import React, { useState, useEffect } from "react";
import { Outlet, Link, useLocation, useNavigate } from "react-router-dom";
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Toast } from 'primereact/toast';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import 'primereact/resources/themes/mdc-light-indigo/theme.css';
import "primereact/resources/primereact.min.css";
import 'primeflex/primeflex.css';
import DiscountAlert from "../components/DiscountAlert";


const Reseller = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Check authentication status
  useEffect(() => {
    const token = sessionStorage.getItem('token');
    setIsAuthenticated(!!token);

    if (!token && location.pathname === '/reseller/affiliate') {
      // Redirect to login if trying to access affiliate section without authentication
      navigate('/login', { state: { returnUrl: location.pathname } });
    }
  }, [location.pathname, navigate]);

  // Redirect to overview tab if on the base reseller path
  useEffect(() => {
    if (location.pathname === '/reseller') {
      navigate('/reseller/overview', { replace: true });
    }
  }, [location.pathname, navigate]);

  // Helper function to determine active tab
  const isActiveTab = (tabName) => {
    const currentPath = location.pathname.split('/').pop();

    // If we're on the base /reseller path with no section, consider 'overview' active
    if (location.pathname === '/reseller' && tabName === 'overview') {
      return 'active';
    }

    // Otherwise check if the current path matches the tab name
    return currentPath === tabName ? 'active' : '';
  };

  // No sample data needed

  // Removed unused payouts data

  // Reseller sections based on the Account.js structure
  const resellerSections = [
    {
      name: 'overview',
      label: 'Overview',
      icon: 'si si-graph',
      content: <OverviewSection />
    },
    {
      name: 'affiliate',
      label: 'Affiliate Program',
      icon: 'si si-link',
      content: isAuthenticated ? <AffiliateSection /> : <AuthRequiredSection />
    },
    {
      name: 'commission',
      label: 'Commission',
      icon: 'si si-wallet',
      content: <CommissionSection />
    },
  ];

  return (
    <>
      <div className="page-header">
        <div className="page-leftheader">
          <ol className="breadcrumb">
            <li className="breadcrumb-item1"><Link to="/">Home</Link></li>
            <li className="breadcrumb-item1">Affiliate</li>
          </ol>
        </div>
        <div className="page-rightheader ml-auto">
          <div className="dropdown">
            <a href="#" className="nav-link pr-0 leading-none" data-toggle="dropdown">
              <button className="btn btn-success">New Service</button>
            </a>
            <div className="dropdown-menu">
              <Link className="dropdown-item d-flex" to="/dedicatedorder">
                <div className="mt-1">Dedicated Server</div>
              </Link>
              <Link className="dropdown-item d-flex" to="/cloudorder">
                <div className="mt-1">Cloud Server</div>
              </Link>

            </div>
          </div>
        </div>
      </div>

      <div className="row">
        <div className="col-12">
          <DiscountAlert />

          <div className="card smaller">
            <div className="card-header">
              <div className="card-title">Reseller Program</div>
            </div>
            <div className="card-body">
              <div className="panel panel-primary">
                <div className="tab-menu-heading">
                  <div className="tabs-menu">
                    <ul className="nav panel-tabs">
                      {resellerSections.map(section => (
                        <li key={section.name}>
                          <Link
                            to={`/reseller/${section.name}`}
                            className={isActiveTab(section.name)}
                          >
                            <i className={`${section.icon}`} style={{ marginRight: '8px', verticalAlign: 'middle' }}></i>
                            {section.label}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
                <div className="panel-body tabs-menu-body">
                  <div className="tab-content">
                    {(() => {
                      // Get the current section from the URL
                      const currentSection = location.pathname.split('/').pop();

                      // Find the matching section
                      const activeSection = resellerSections.find(section =>
                        section.name === currentSection
                      );

                      // If no matching section found, show Overview
                      if (!activeSection) {
                        return resellerSections.find(section => section.name === 'overview').content;
                      }

                      // Otherwise show the matching section
                      return activeSection.content;
                    })()}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

// Overview Section Component
const OverviewSection = () => {
  // Sample special promotions data
  const specialPromos = [
    { name: "Affiliate Bonus", description: "Earn €100 bonus when you refer 5 new customers this month", expires: "April 30, 2025" },
    { name: "Tier Upgrade Fast-Track", description: "Qualify for Gold tier with just 30 active services (normally 50)", expires: "May 15, 2025" },
    { name: "Commission Boost", description: "Get an extra 3% commission on all new sign-ups", expires: "Ongoing" }
  ];

  const [commissionRates, setCommissionRates] = useState([]);
  const [currentLevel, setCurrentLevel] = useState('');
  const [currentRate, setCurrentRate] = useState('0%');
  // Initialize service count to 0
  const [serviceCount, setServiceCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [nextLevel, setNextLevel] = useState(null);
  const [servicesToNextLevel, setServicesToNextLevel] = useState(0);
  const [progressPercentage, setProgressPercentage] = useState(0);

  // Fetch commission rates from the database
  useEffect(() => {
    const fetchCommissionRates = async () => {
      try {
        const token = sessionStorage.getItem('token');
        if (!token) {
          setLoading(false);
          return;
        }

        console.log("Fetching commission rates with token:", token);
        const response = await fetch('/api.php?f=get_commission_rates', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ token })
        });

        console.log("Response status:", response.status);
        if (!response.ok) {
          console.error("API response not OK:", response.status, response.statusText);
          throw new Error(`API response error: ${response.status} ${response.statusText}`);
        }

        const responseText = await response.text();
        console.log("Raw API response:", responseText);

        let data;
        try {
          // Parse the response and add safety checks
          data = JSON.parse(responseText);
          console.log("Raw API response:", data);

          // CRITICAL FIX: Ensure data is a valid object
          if (typeof data !== 'object' || data === null) {
            console.error("API response is not an object - creating default response");
            data = { error: 1, message: "Invalid response format", referral_count: 0, service_count: 0 };
          }

          // CRITICAL FIX: Check for both referral_count and service_count properties
          let referralCount = 0;

          if (data.hasOwnProperty('referral_count')) {
            // Use referral_count if it exists
            referralCount = parseInt(data.referral_count || 0, 10);
            console.log("Using referral_count from API:", referralCount);
          } else if (data.hasOwnProperty('service_count')) {
            // Fall back to service_count if referral_count doesn't exist
            referralCount = parseInt(data.service_count || 0, 10);
            console.log("Using service_count from API:", referralCount);

            // Add referral_count property for consistency
            data.referral_count = referralCount;
          } else {
            console.warn("API response missing both referral_count and service_count - using default value of 0");
            data.referral_count = 0;
            data.service_count = 0;
          }

          // Ensure the count is a valid number
          if (isNaN(referralCount)) {
            console.warn("Count value is NaN after parsing - using default value of 0");
            referralCount = 0;
            data.referral_count = 0;
            data.service_count = 0;
          }

          // Log the final count for debugging
          console.log("Final referral count:", referralCount);

          console.log("Final parsed referral count:", data.referral_count);
        } catch (parseError) {
          console.error("Error parsing JSON:", parseError);
          console.error("Invalid JSON response:", responseText);

          // CRITICAL FIX: Create a default data object with both count properties
          data = {
            error: 1,
            message: "Invalid JSON response",
            referral_count: 0,
            service_count: 0,  // Include both properties for compatibility
            rates: [
              { level: 'Bronze', requirement: '5+ Active Referrals', min_services: 5, rate: 5 },
              { level: 'Silver', requirement: '20+ Active Referrals', min_services: 20, rate: 10 },
              { level: 'Gold', requirement: '50+ Active Referrals', min_services: 50, rate: 15 },
              { level: 'Platinum', requirement: '100+ Active Referrals', min_services: 100, rate: 20 }
            ],
            user_level: 'Bronze'
          };

          console.warn("Using default data object with referral_count = 0");
        }

        if (data.error === 0 && data.rates) {
          console.log("Commission rates from API:", data.rates);
          setCommissionRates(data.rates);

          // Set current level and referral count
          if (data.user_level) {
            setCurrentLevel(data.user_level);
            // CRITICAL FIX: Get the referral count from either property
            // We've already normalized this above, so we can just use referral_count
            const referralCount = data.referral_count || 0;

            console.log("CRITICAL FIX - Using normalized referral count:", referralCount);

            // Log the actual referred users count for comparison
            fetch('/api.php?f=get_referred_users', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({ token })
            })
            .then(response => response.json())
            .then(users => {
              console.log("Actual referred users count:", users.length);
              console.log("Referral count from API:", referralCount);
              if (users.length !== referralCount) {
                console.warn("Mismatch between referral count and actual referred users!");
              }
            })
            .catch(error => {
              console.error("Error checking referred users:", error);
            });

            // CRITICAL FIX: Ultra-safe state update
            // Ensure we're setting a valid number to the state
            const safeCount = typeof referralCount === 'number' && !isNaN(referralCount)
              ? referralCount
              : 0;

            console.log("CRITICAL FIX - Setting serviceCount state to:", safeCount);
            setServiceCount(safeCount);

            // Find current rate
            const userRate = data.rates.find(rate => rate.level === data.user_level);
            if (userRate) {
              setCurrentRate(userRate.rate + '%');
            }

            // Find next level and referrals needed
            const currentLevelIndex = data.rates.findIndex(rate => rate.level === data.user_level);
            console.log("Current level index:", currentLevelIndex);

            if (currentLevelIndex < data.rates.length - 1) {
              const nextLevelData = data.rates[currentLevelIndex + 1];
              console.log("Next level data:", nextLevelData);
              setNextLevel(nextLevelData);

              // Get min_services from the API response
              const currentRate = data.rates[currentLevelIndex];
              const nextRate = nextLevelData;

              console.log("Current rate min_services:", currentRate.min_services);
              console.log("Next rate min_services:", nextRate.min_services);

              // Parse min_services as integers to ensure proper calculation
              const currentMin = parseInt(currentRate.min_services || 0, 10);
              const nextMin = parseInt(nextRate.min_services || 0, 10);

              // Use the same referralCount we calculated earlier - don't parse it again
              console.log("Using previously calculated referralCount:", referralCount);

              console.log("Parsed currentMin:", currentMin);
              console.log("Parsed nextMin:", nextMin);
              console.log("Parsed referralCount:", referralCount);

              // Calculate needed referrals and ensure it's a valid number
              let neededReferrals = Math.max(0, nextMin - referralCount);
              if (isNaN(neededReferrals)) {
                console.error("Invalid calculation for needed referrals. Using default value.");
                neededReferrals = nextMin; // Default to the full amount needed for next level
              }

              console.log("Needed referrals:", neededReferrals);
              setServicesToNextLevel(neededReferrals);

              // Calculate progress percentage
              let progress = 0;
              if (nextMin > currentMin) {
                progress = ((referralCount - currentMin) / (nextMin - currentMin)) * 100;
              } else {
                // Handle case where nextMin <= currentMin (should not happen, but just in case)
                progress = 100;
              }

              // Handle NaN or invalid values
              if (isNaN(progress)) {
                console.error("Invalid progress calculation. Using default value of 0.");
                progress = 0;
              }

              const clampedProgress = Math.min(Math.max(progress, 0), 100); // Clamp between 0-100
              console.log("Progress percentage:", clampedProgress);
              setProgressPercentage(clampedProgress);
            } else {
              // Already at highest level
              setNextLevel(null);
              setServicesToNextLevel(0);
              setProgressPercentage(100);
            }
          }
        } else {
          console.log("API call failed or returned no rates, using default rates");
          // If API call fails, use default rates
          const defaultRates = [
            { level: 'Bronze', requirement: '5+ Active Referrals', min_services: 5, rate: 5 },
            { level: 'Silver', requirement: '20+ Active Referrals', min_services: 20, rate: 10 },
            { level: 'Gold', requirement: '50+ Active Referrals', min_services: 50, rate: 15 },
            { level: 'Platinum', requirement: '100+ Active Referrals', min_services: 100, rate: 20 }
          ];
          setCommissionRates(defaultRates);
          setCurrentLevel('Bronze');
          setCurrentRate('5%');
          setServiceCount(0); // Default to 0 referrals

          // Set default next level (Silver)
          setNextLevel(defaultRates[1]);
          setServicesToNextLevel(defaultRates[1].min_services);
          setProgressPercentage(0);
        }
      } catch (error) {
        console.error('Error fetching commission rates:', error);
        // Use default rates if fetch fails
        const defaultRates = [
          { level: 'Bronze', requirement: '5+ Active Referrals', min_services: 5, rate: 5 },
          { level: 'Silver', requirement: '20+ Active Referrals', min_services: 20, rate: 10 },
          { level: 'Gold', requirement: '50+ Active Referrals', min_services: 50, rate: 15 },
          { level: 'Platinum', requirement: '100+ Active Referrals', min_services: 100, rate: 20 }
        ];
        setCommissionRates(defaultRates);
        setCurrentLevel('Bronze');
        setCurrentRate('5%');
        setServiceCount(0); // Default to 0 referrals

        // Set default next level (Silver)
        setNextLevel(defaultRates[1]);
        setServicesToNextLevel(defaultRates[1].min_services);
        setProgressPercentage(0);
      } finally {
        setLoading(false);
      }
    };

    fetchCommissionRates();
  }, []);

  // Map levels to benefits
  const levelBenefits = {
    'Bronze': 'Standard support',
    'Silver': 'Priority support',
    'Gold': 'Priority support + Dedicated account manager',
    'Platinum': 'Premium support + Dedicated account manager + Custom solutions'
  };

  return (
    <div className="tab-pane active">
      <div className="row">
        <div className="col-xl-4 col-md-12">
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Reseller Status</h3>
            </div>
            <div className="card-body">
              {loading ? (
                <div className="text-center p-4">
                  <i className="pi pi-spin pi-spinner" style={{ fontSize: '2rem' }}></i>
                  <p className="mt-2">Loading status...</p>
                </div>
              ) : (
                <div className="text-center">
                  <h2 className="mb-0 fs-50 mt-3 counter font-weight-bold">{currentLevel}</h2>
                  <p>Current Reseller Level</p>
                  <div className="progress h-2 mt-2 mb-2">
                    <div
                      className="progress-bar bg-primary"
                      role="progressbar"
                      style={{ width: `${progressPercentage}%` }}
                    ></div>
                  </div>
                  <div className="d-flex justify-content-between">
                    <span>{currentLevel}</span>
                    <span>{nextLevel ? nextLevel.level : currentLevel}</span>
                  </div>
                  <p className="mt-3">
                    <strong>Current Commission Rate:</strong> {currentRate}<br/>
                    <strong>Active Referrals:</strong> {(() => {
                      // CRITICAL FIX: Ultra-safe display of referral count
                      // This ensures we always display a valid number even if state is somehow corrupted
                      try {
                        const count = parseInt(serviceCount, 10);
                        return isNaN(count) ? 0 : count;
                      } catch (e) {
                        return 0;
                      }
                    })()}<br/>
                    {nextLevel && (
                      <><strong>{servicesToNextLevel} more</strong> active referrals to reach {nextLevel.level} level</>
                    )}
                    {!nextLevel && (
                      <><strong>Congratulations!</strong> You've reached the highest level</>
                    )}
                  </p>
                </div>
              )}
              {nextLevel && (
                <div className="mt-4">
                  <Link to="/reseller/affiliate">
                    <button className="btn btn-primary btn-block">Upgrade to {nextLevel.level}</button>
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="col-xl-8 col-md-12">
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Program Benefits</h3>
            </div>
            <div className="card-body">
              {loading ? (
                <div className="text-center p-4">
                  <i className="pi pi-spin pi-spinner" style={{ fontSize: '2rem' }}></i>
                  <p className="mt-2">Loading benefits...</p>
                </div>
              ) : (
                <div className="table-responsive">
                  <table className="table card-table text-nowrap mb-0">
                    <thead>
                      <tr>
                        <th>Level</th>
                        <th>Required Referrals</th>
                        <th>Commission</th>
                        <th>Benefits</th>
                      </tr>
                    </thead>
                    <tbody>
                      {commissionRates.map((rate, index) => (
                        <tr key={index} className={currentLevel === rate.level ? 'bg-light' : ''}>
                          <td><strong>{rate.level}</strong></td>
                          <td>{rate.requirement}</td>
                          <td><strong>{rate.rate}%</strong></td>
                          <td>{levelBenefits[rate.level] || 'Standard support'}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="row mt-4">
      <div className="col-xl-12 col-md-12">
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Marketing Resources</h3>
            </div>
            <div className="card-body">
              <p>Use these marketing resources to promote ZetServers to your affiliates. Your unique tracking link will be automatically included in all resources.</p>
              <div className="row">
                <div className="col-md-4">
                  <div className="card">
                    <div className="card-body">
                      <h5 className="card-title">Banners & Logos</h5>
                      <p className="card-text">Download banners and logos in various formats and sizes for your website.</p>
                      <a href="#" className="btn btn-primary">Download</a>
                    </div>
                  </div>
                </div>
                <div className="col-md-4">
                  <div className="card">
                    <div className="card-body">
                      <h5 className="card-title">Product Sheets</h5>
                      <p className="card-text">PDFs with detailed information about our products and services.</p>
                      <a href="#" className="btn btn-primary">Download</a>
                    </div>
                  </div>
                </div>
                <div className="col-md-4">
                  <div className="card">
                    <div className="card-body">
                      <h5 className="card-title">Email Templates</h5>
                      <p className="card-text">Ready-to-use email templates for promoting our services.</p>
                      <a href="#" className="btn btn-primary">Download</a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};


// Updated CommissionSection component with voucher management
const CommissionSection = () => {
  const [commissionRates, setCommissionRates] = useState([]);
  const [currentRate, setCurrentRate] = useState('0%');
  const [currentLevel, setCurrentLevel] = useState('');
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('available-vouchers');
  const [availableVouchers, setAvailableVouchers] = useState([]);
  const [usedVouchers, setUsedVouchers] = useState([]);
  const [loadingVouchers, setLoadingVouchers] = useState(false);
  const toastRef = React.useRef(null);

  // Fetch commission rates from the database
  useEffect(() => {
    const fetchCommissionRates = async () => {
      try {
        const token = sessionStorage.getItem('token');
        if (!token) {
          setLoading(false);
          return;
        }

        console.log("CommissionSection: Fetching commission rates with token:", token);
        const response = await fetch('/api.php?f=get_commission_rates', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ token })
        });

        console.log("CommissionSection: Response status:", response.status);
        if (!response.ok) {
          console.error("CommissionSection: API response not OK:", response.status, response.statusText);
          throw new Error(`API response error: ${response.status} ${response.statusText}`);
        }

        const responseText = await response.text();
        console.log("CommissionSection: Raw API response:", responseText);

        let data;
        try {
          data = JSON.parse(responseText);
          console.log("CommissionSection: API response:", data);
        } catch (parseError) {
          console.error("CommissionSection: Error parsing JSON:", parseError);
          throw new Error(`Invalid JSON response: ${responseText}`);
        }

        if (data.error === 0 && data.rates) {
          setCommissionRates(data.rates);

          // Set current rate based on user's level
          if (data.user_level) {
            setCurrentLevel(data.user_level);
            console.log("CommissionSection: User level set to", data.user_level);
            console.log("CommissionSection: Referral count", data.referral_count);

            const userRate = data.rates.find(rate => rate.level === data.user_level);
            if (userRate) {
              console.log("CommissionSection: User rate found", userRate);
              setCurrentRate(userRate.rate + '%');
            }
          }
        } else {
          console.log("CommissionSection: API call failed or returned no rates, using default rates");
          // If API call fails, use default rates
          const defaultRates = [
            { level: 'Bronze', requirement: '5+ Active Referrals', min_services: 5, rate: 5 },
            { level: 'Silver', requirement: '20+ Active Referrals', min_services: 20, rate: 10 },
            { level: 'Gold', requirement: '50+ Active Referrals', min_services: 50, rate: 15 },
            { level: 'Platinum', requirement: '100+ Active Referrals', min_services: 100, rate: 20 }
          ];
          setCommissionRates(defaultRates);
          setCurrentLevel('Bronze');
          setCurrentRate('5%');
        }
      } catch (error) {
        console.error('Error fetching commission rates:', error);
        // Use default rates if fetch fails
        const defaultRates = [
          { level: 'Bronze', requirement: '5+ Active Referrals', min_services: 5, rate: 5 },
          { level: 'Silver', requirement: '20+ Active Referrals', min_services: 20, rate: 10 },
          { level: 'Gold', requirement: '50+ Active Referrals', min_services: 50, rate: 15 },
          { level: 'Platinum', requirement: '100+ Active Referrals', min_services: 100, rate: 20 }
        ];
        setCommissionRates(defaultRates);
        setCurrentLevel('Bronze');
        setCurrentRate('5%');
      } finally {
        setLoading(false);
      }
    };

    fetchCommissionRates();
  }, []);

  // Fetch vouchers from the database
  const fetchVouchers = async () => {
    setLoadingVouchers(true);
    try {
      const token = sessionStorage.getItem('token');
      if (!token) {
        setLoadingVouchers(false);
        return;
      }

      // Fetch available vouchers
      const availableResponse = await fetch('/api.php?f=get_vouchers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token, status: 'active' })
      });

      const availableData = await availableResponse.json();

      if (availableData.error === 0 && availableData.vouchers) {
        setAvailableVouchers(availableData.vouchers);
      } else {
        setAvailableVouchers([]);
      }

      // Fetch used vouchers
      const usedResponse = await fetch('/api.php?f=get_vouchers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token, status: 'used' })
      });

      const usedData = await usedResponse.json();

      if (usedData.error === 0 && usedData.vouchers) {
        setUsedVouchers(usedData.vouchers);
      } else {
        setUsedVouchers([]);
      }
    } catch (error) {
      console.error('Error fetching vouchers:', error);
      toastRef.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to fetch vouchers',
        life: 3000
      });
    } finally {
      setLoadingVouchers(false);
    }
  };



  // Load vouchers on component mount
  useEffect(() => {
    fetchVouchers();
  }, []);

  return (
    <div className="tab-pane active">
      <Toast ref={toastRef} />
      <div className="row">
        <div className="col-xl-4 col-lg-6 col-md-12">
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Commission Rate</h3>
            </div>
            <div className="card-body">
              {loading ? (
                <div className="text-center p-4">
                  <i className="pi pi-spin pi-spinner" style={{ fontSize: '2rem' }}></i>
                  <p className="mt-2">Loading commission rates...</p>
                </div>
              ) : (
                <>
                  <div className="text-center">
                    <h2 className="mb-0 fs-50 mt-3 counter font-weight-bold">{currentRate}</h2>
                    <p>Current Commission Rate</p>
                  </div>
                  <div className="alert alert-info mt-3">
                    <i className="fa fa-info-circle mr-2"></i>
                    When an affiliate signs up, they receive a voucher with a percentage discount based on your current level.
                  </div>
                  <div className="table-responsive mt-4">
                    <table className="table card-table text-nowrap mb-0">
                      <thead>
                        <tr>
                          <th>Level</th>
                          <th>Requirement</th>
                          <th>Rate</th>
                        </tr>
                      </thead>
                      <tbody>
                        {commissionRates.map((rate, index) => (
                          <tr key={index} className={currentLevel === rate.level ? 'bg-light' : ''}>
                            <td>{currentLevel === rate.level ? <b>{rate.level}</b> : rate.level}</td>
                            <td>{currentLevel === rate.level ? <b>{rate.requirement}</b> : rate.requirement}</td>
                            <td>{currentLevel === rate.level ? <b>{rate.rate}%</b> : rate.rate + '%'}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>

        <div className="col-xl-8 col-lg-6 col-md-12">
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Voucher Management</h3>
            </div>
            <div className="card-body">
              <ul className="nav nav-tabs" role="tablist">
                <li className="nav-item" style={{ width: '180px', textAlign: 'center' }}>
                  <a
                    className={`nav-link ${activeTab === 'available-vouchers' ? 'active' : ''}`}
                    onClick={() => setActiveTab('available-vouchers')}
                    data-toggle="tab"
                    href="#available-vouchers"
                    role="tab"
                    style={{
                      color: activeTab === 'available-vouchers' ? 'white' : '#495057',
                      width: '100%',
                      display: 'block',
                      height: '40px',
                      lineHeight: '24px',
                      padding: '8px 16px'
                    }}
                  >
                    <span>Available Vouchers</span>
                  </a>
                </li>
                <li className="nav-item" style={{ width: '180px', textAlign: 'center' }}>
                  <a
                    className={`nav-link ${activeTab === 'used-vouchers' ? 'active' : ''}`}
                    onClick={() => setActiveTab('used-vouchers')}
                    data-toggle="tab"
                    href="#used-vouchers"
                    role="tab"
                    style={{
                      color: activeTab === 'used-vouchers' ? 'white' : '#495057',
                      width: '100%',
                      display: 'block',
                      height: '40px',
                      lineHeight: '24px',
                      padding: '8px 16px'
                    }}
                  >
                    <span>Used Vouchers</span>
                  </a>
                </li>
              </ul>

              <div className="tab-content mt-3">
                <div className={`tab-pane ${activeTab === 'available-vouchers' ? 'active' : ''}`} id="available-vouchers" role="tabpanel">
                  {loading || loadingVouchers ? (
                    <div className="text-center p-4">
                      <i className="pi pi-spin pi-spinner" style={{ fontSize: '2rem' }}></i>
                      <p className="mt-2">Loading available vouchers...</p>
                    </div>
                  ) : (
                    <div className="table-responsive">
                      <table className="table card-table text-nowrap mb-0">
                        <thead>
                          <tr>
                            <th>Voucher Code</th>
                            <th>Value</th>
                            <th>Generated By</th>
                            <th>Created Date</th>
                            <th>Expires</th>
                            <th>Status</th>
                          </tr>
                        </thead>
                        <tbody>
                          {availableVouchers.length > 0 ? (
                            availableVouchers.map((voucher, index) => (
                              <tr key={index}>
                                <td>{voucher.voucher_code}</td>
                                <td>€{parseFloat(voucher.value).toFixed(2)}</td>
                                <td>{voucher.generator_masked_name || 'System'}</td>
                                <td>{new Date(voucher.created_date).toLocaleDateString('en-GB', {day: '2-digit', month: '2-digit', year: 'numeric'})}</td>
                                <td>{new Date(voucher.expiry_date).toLocaleDateString('en-GB', {day: '2-digit', month: '2-digit', year: 'numeric'})}</td>
                                <td><span className="badge bg-success">Active</span></td>
                              </tr>
                            ))
                          ) : (
                            <tr>
                              <td colSpan="6" className="text-center">
                                <div className="alert alert-info mb-0">
                                  <i className="fa fa-info-circle mr-2"></i>
                                  No active vouchers available.
                                </div>
                              </td>
                            </tr>
                          )}

                        </tbody>
                      </table>
                    </div>
                  )}
                </div>

                <div className={`tab-pane ${activeTab === 'used-vouchers' ? 'active' : ''}`} id="used-vouchers" role="tabpanel">
                  {loading || loadingVouchers ? (
                    <div className="text-center p-4">
                      <i className="pi pi-spin pi-spinner" style={{ fontSize: '2rem' }}></i>
                      <p className="mt-2">Loading used vouchers...</p>
                    </div>
                  ) : (
                    <div className="table-responsive">
                      <table className="table card-table text-nowrap mb-0">
                        <thead>
                          <tr>
                            <th>Voucher Code</th>
                            <th>Value</th>
                            <th>Generated By</th>
                            <th>Used By</th>
                            <th>Used Date</th>
                            <th>Order Value</th>
                          </tr>
                        </thead>
                        <tbody>
                          {usedVouchers.length > 0 ? (
                            usedVouchers.map((voucher, index) => (
                              <tr key={index}>
                                <td>{voucher.voucher_code}</td>
                                <td>€{parseFloat(voucher.value).toFixed(2)}</td>
                                <td>{voucher.generator_masked_name || 'System'}</td>
                                <td>{voucher.masked_name || 'Unknown'}</td>
                                <td>{voucher.used_at ? new Date(voucher.used_at).toLocaleDateString('en-GB', {day: '2-digit', month: '2-digit', year: 'numeric'}) : 'N/A'}</td>
                                <td>{voucher.order_value ? `€${parseFloat(voucher.order_value).toFixed(2)}` : 'N/A'}</td>
                              </tr>
                            ))
                          ) : (
                            <tr>
                              <td colSpan="6" className="text-center">
                                <div className="alert alert-info mb-0">
                                  <i className="fa fa-info-circle mr-2"></i>
                                  No vouchers have been used yet.
                                </div>
                              </td>
                            </tr>
                          )}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>


    </div>
  );
};



// Affiliate Section Component
const AffiliateSection = () => {
  const [affiliateLink, setAffiliateLink] = useState('');
  const [loading, setLoading] = useState(false);
  const [referredUsers, setReferredUsers] = useState([]);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const toastRef = React.useRef(null);

  // Function to generate affiliate link
  const generateAffiliateLink = async () => {
    setLoading(true);
    try {
      const token = sessionStorage.getItem('token');

      if (!token) {
        toastRef.current.show({
          severity: 'error',
          summary: 'Authentication Error',
          detail: 'You are not logged in. Please log in and try again.',
          life: 3000
        });
        setLoading(false);
        return;
      }

      const response = await fetch('/api.php?f=generate_affiliate_link', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      const data = await response.json();

      if (data.error === 0) {
        setAffiliateLink(data.full_link);
        // Success message removed as requested
      } else {
        toastRef.current.show({
          severity: 'error',
          summary: 'Error',
          detail: data.message || 'Failed to generate affiliate link',
          life: 3000
        });
      }
    } catch (error) {
      console.error('Error generating affiliate link:', error);
      toastRef.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to generate affiliate link',
        life: 3000
      });
    } finally {
      setLoading(false);
    }
  };

  // Function to copy affiliate link to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(affiliateLink).then(
      () => {
        toastRef.current.show({
          severity: 'success',
          summary: 'Copied!',
          detail: 'Link copied to clipboard',
          life: 3000
        });
      },
      (err) => {
        console.error('Could not copy text: ', err);
        toastRef.current.show({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to copy to clipboard',
          life: 3000
        });
      }
    );
  };

  // Function to get referred users
  const getReferredUsers = async () => {
    setLoadingUsers(true);
    try {
      const token = sessionStorage.getItem('token');

      if (!token) {
        toastRef.current.show({
          severity: 'error',
          summary: 'Authentication Error',
          detail: 'You are not logged in. Please log in and try again.',
          life: 3000
        });
        setLoadingUsers(false);
        return;
      }

      const response = await fetch('/api.php?f=get_referred_users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      const data = await response.json();
      console.log("Referred users data:", data);
      console.log("Number of referred users:", data.length);

      // Compare with the referral count from commission rates API
      fetch('/api.php?f=get_commission_rates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      })
      .then(response => response.json())
      .then(commissionData => {
        if (commissionData.error === 0) {
          const referralCount = parseInt(commissionData.referral_count || 0, 10);
          console.log("AffiliateSection: Referral count from API:", referralCount);
          console.log("AffiliateSection: Actual referred users count:", data.length);

          if (data.length !== referralCount) {
            console.warn("AffiliateSection: Mismatch between referral count and actual referred users!");
          }
        }
      })
      .catch(error => {
        console.error("Error checking commission rates:", error);
      });

      setReferredUsers(data);
    } catch (error) {
      console.error('Error fetching referred users:', error);
      toastRef.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to fetch referred users',
        life: 3000
      });
    } finally {
      setLoadingUsers(false);
    }
  };

  // Load affiliate link and referred users on component mount
  useEffect(() => {
    const token = sessionStorage.getItem('token');

    if (token) {
      generateAffiliateLink();
      getReferredUsers();
    } else {
      toastRef.current.show({
        severity: 'error',
        summary: 'Authentication Required',
        detail: 'Please log in to access the affiliate program',
        life: 5000
      });
    }
  }, []);

  return (
    <div className="tab-pane active">
      <Toast ref={toastRef} />

      <div className="row">
        <div className="col-xl-12 col-md-12">
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Your Affiliate Link</h3>
            </div>
            <div className="card-body">
              <p>Share this link with your friends and colleagues. When they register using this link, they will be added to your affiliate list.</p>

              <div className="input-group mb-3">
                <InputText
                  value={affiliateLink}
                  readOnly
                  className="form-control"
                  placeholder="Your affiliate link will appear here"
                  style={{ minHeight: '45px' }}
                />
                <div className="input-group-append">
                  <Button
                    label="Copy"
                    icon="pi pi-copy"
                    onClick={copyToClipboard}
                    disabled={!affiliateLink}
                    className="btn btn-primary"
                  />
                </div>
              </div>

              <Button
                label="Generate New Link"
                icon={loading ? "pi pi-spin pi-spinner" : "pi pi-refresh"}
                onClick={generateAffiliateLink}
                disabled={loading}
                className="btn btn-secondary"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="row mt-4">
        <div className="col-xl-12 col-md-12">
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Your Referred Users</h3>
            </div>
            <div className="card-body">
              {loadingUsers ? (
                <div className="text-center p-4">
                  <i className="pi pi-spin pi-spinner" style={{ fontSize: '2rem' }}></i>
                  <p className="mt-2">Loading referred users...</p>
                </div>
              ) : referredUsers.length > 0 ? (
                <DataTable
                  value={referredUsers}
                  paginator
                  rows={10}
                  rowsPerPageOptions={[5, 10, 25, 50]}
                  className="p-datatable-sm"
                  resizableColumns={false}
                  columnResizeMode="fit"
                  tableStyle={{ tableLayout: 'fixed' }}
                >
                  <Column
                    field="name"
                    header="Name"
                    sortable
                    style={{ width: '35%' }}
                    body={(rowData) => {
                      // Mask last name to show only first name + first letter of last name + ***
                      const fullName = rowData.name || '';
                      const nameParts = fullName.split(' ');

                      if (nameParts.length > 1) {
                        const firstName = nameParts[0];
                        const lastNameInitial = nameParts[1].charAt(0);
                        return firstName + ' ' + lastNameInitial + '***';
                      }

                      return fullName;
                    }}
                  />
                  <Column
                    field="email"
                    header="Email"
                    sortable
                    style={{ width: '35%' }}
                    body={(rowData) => {
                      // Mask email to show only first 3 characters + *** + domain
                      const email = rowData.email || '';
                      if (email.includes('@')) {
                        const [username, domain] = email.split('@');
                        const maskedUsername = username.substring(0, 3) + '***';
                        return maskedUsername + '@' + domain;
                      }
                      return email;
                    }}
                  />
                  <Column
                    field="registered"
                    header="Registered Date"
                    sortable
                    style={{ width: '30%' }}
                  />
                </DataTable>
              ) : (
                <div className="alert alert-info">
                  <i className="fa fa-info-circle mr-2"></i>
                  You haven't referred any users yet. Share your affiliate link to start earning commissions!
                </div>
              )}

              <div className="mt-3">
                <Button
                  label="Refresh List"
                  icon={loadingUsers ? "pi pi-spin pi-spinner" : "pi pi-refresh"}
                  onClick={getReferredUsers}
                  disabled={loadingUsers}
                  className="btn btn-secondary"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="row mt-4">
        <div className="col-xl-12 col-md-12">
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Affiliate Program Benefits</h3>
            </div>
            <div className="card-body">
              <div className="row">
                <div className="col-md-6">
                  <h4>How It Works</h4>
                  <ol>
                    <li>Share your unique affiliate link with potential customers</li>
                    <li>When they register using your link, they're added to your affiliate list</li>
                    <li>They receive a voucher with a value based on your Current Commission Rate</li>
                    <li>Track your referrals in your dashboard</li>
                  </ol>
                </div>
                <div className="col-md-6">
                  <h4>Voucher System</h4>
                  <ul className="list-unstyled">
                    <li><i className="fa fa-check text-success mr-2"></i> New affiliates receive a voucher</li>
                    <li><i className="fa fa-check text-success mr-2"></i> Voucher value based on your Current Commission Rate</li>
                    <li><i className="fa fa-check text-success mr-2"></i> Voucher received for each of their invoice payments</li>
                    <li><i className="fa fa-check text-success mr-2"></i> No limit on the number of referrals</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Auth Required Section Component
const AuthRequiredSection = () => {
  const navigate = useNavigate();

  return (
    <div className="tab-pane active">
      <div className="row">
        <div className="col-xl-12 col-md-12">
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Authentication Required</h3>
            </div>
            <div className="card-body text-center">
              <div className="mb-4">
                <i className="si si-lock" style={{ fontSize: '48px', color: '#dc3545' }}></i>
              </div>
              <h4>Please log in to access the Affiliate Program</h4>
              <p className="text-muted">You need to be logged in to view and manage your affiliate links.</p>
              <button
                className="btn btn-primary mt-3"
                onClick={() => navigate('/login', { state: { returnUrl: '/reseller/affiliate' } })}
              >
                Go to Login
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Reseller;