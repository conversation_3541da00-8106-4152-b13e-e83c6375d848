import React, { useEffect, useState, useRef } from "react"
import { Outlet, Link, useLocation, useNavigate, useParams } from "react-router-dom";
import DiscountAlert from "../components/DiscountAlert";

const Ticket = () => {
  const navigate = useNavigate(); 
  const { id: ticketId } = useParams();
  const [messages, setMessages] = useState([]);
  const [ticket, setTicket] = useState(null);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [notification, setNotification] = useState({ show: false, message: '', type: 'success' });
  const messageInputRef = useRef(null);
  const messagesEndRef = useRef(null);
  
  // Add access control state
  const [hasAccess, setHasAccess] = useState(null); // Start with null to show loading
  const [accessError, setAccessError] = useState('');
  
  // Function to get token from both storage locations
  function getToken() {
    const sessionToken = sessionStorage.getItem('token');
    const localToken = localStorage.getItem('token');
    return sessionToken || localToken;
  }

  // No Access Component
  const NoAccessMessage = () => (
    <div className="text-center p-5">
      <i className="fa fa-lock fa-4x text-danger mb-4"></i>
      <h3 className="text-danger mb-3">Access Denied</h3>
      <p className="text-muted mb-4" style={{fontSize: '1.1rem'}}>
        {accessError || 'You do not have permission to access this support ticket.'}
      </p>
      <p className="text-muted">
        Please contact your administrator if you believe this is an error.
      </p>
      <div className="mt-4">
        <button 
          className="btn btn-secondary me-2" 
          onClick={() => window.location.reload()}
        >
          <i className="fa fa-refresh me-1"></i> Refresh Page
        </button>
        <Link to="/support" className="btn btn-primary">
          <i className="fa fa-arrow-left me-1"></i> Back to Support
        </Link>
      </div>
    </div>
  );
  
  // Scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  // Fetch ticket details
  const fetchTicketDetails = () => {
    const token = getToken();
    if (!token) {
      setNotification({
        show: true,
        message: 'Authentication required. Please log in.',
        type: 'error'
      });
      return;
    }
    
    // Pass ticket ID as URL parameter instead of in the request body
    fetch(`/api.php?f=ticket_details&id=${ticketId}`, {
      method: "POST",
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ token })
    })
    .then(response => {
      console.log("Ticket Details Response status:", response.status);
      
      // Check for 403 status code BEFORE trying to parse JSON
      if (response.status === 403) {
        console.log("403 detected - setting no access for ticket details");
        setHasAccess(false);
        setAccessError('You do not have permission to access this support ticket');
        throw new Error('Access denied');
      }
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return response.json();
    })
    .then(data => {
      console.log("Ticket Details Response Data:", data);
      
      if (data.error) {
        console.error("Error fetching ticket:", data);
        if (data.message && (data.message.includes('permission') || data.message.includes('Access denied'))) {
          setHasAccess(false);
          setAccessError(data.message || 'Access denied');
          return;
        }
        setNotification({
          show: true,
          message: data.message || 'Error loading ticket details',
          type: 'error'
        });
        return;
      }
      
      // Success
      setTicket(data);
      setHasAccess(true);
    })
    .catch(error => {
      console.error("Error fetching ticket details:", error);
      if (error.message !== 'Access denied') {
        setNotification({
          show: true,
          message: 'Network error loading ticket details',
          type: 'error'
        });
      }
    });
  };

  // Fetch messages
  const fetchMessages = () => {
    setLoading(true);
    const token = getToken();
    
    fetch(`/api.php?f=messages&id=${ticketId}`, {
      method: "POST",
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ token })
    })
    .then(response => {
      console.log("Messages Response status:", response.status);
      
      // Check for 403 status code
      if (response.status === 403) {
        console.log("403 detected - setting no access for messages");
        setHasAccess(false);
        setAccessError('You do not have permission to access this support ticket');
        setLoading(false);
        throw new Error('Access denied');
      }
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return response.json();
    })
    .then(data => {
      console.log("Messages Response Data:", data);
      
      if (data.error) {
        if (data.message && (data.message.includes('permission') || data.message.includes('Access denied'))) {
          setHasAccess(false);
          setAccessError(data.message || 'Access denied');
          setLoading(false);
          return;
        }
      }
      
      // Success
      setMessages(data);
      setHasAccess(true);
      setLoading(false);
    })
    .catch(error => {
      console.error("Error fetching messages:", error);
      if (error.message !== 'Access denied') {
        // Handle other errors normally
      }
      setLoading(false);
    });
  };

  // Send a new message
  const sendMessage = () => {
    const messageText = messageInputRef.current.value.trim();
    if (!messageText) return;
    
    setSending(true);
    const token = getToken();
    
    if (!token) {
      setNotification({
        show: true,
        message: 'Authentication required. Please log in.',
        type: 'error'
      });
      setSending(false);
      return;
    }
    
    fetch("/api.php?f=add_message", {
      method: "POST",
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        ticket_id: ticketId,
        message: messageText,
        token
      })
    })
    .then(response => {
      // Handle 403 response
      if (response.status === 403) {
        setNotification({
          show: true,
          message: 'Access denied: You do not have permission to add messages to this ticket',
          type: 'error'
        });
        setSending(false);
        return null;
      }
      
      return response.json();
    })
    .then(data => {
      if (!data) return; // Handle 403 case
      
      if (data.success) {
        // Add message to state for immediate feedback
        setMessages([...messages, {
          id: Date.now(), // Temporary ID
          type: 'customer',
          time: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}),
          message: messageText
        }]);
        
        messageInputRef.current.value = '';
 
        // Refetch messages to get the accurate state from server
        setTimeout(fetchMessages, 500);
      } else {
        console.error("Failed to send message:", data.error);
        setNotification({
          show: true,
          message: data.message || "Failed to send message. Please try again.",
          type: 'error'
        });
      }
      setSending(false);
    })
    .catch(error => {
      console.error("Error sending message:", error);
      setNotification({
        show: true,
        message: "Network error. Please try again.",
        type: 'error'
      });
      setSending(false);
    });
  };

  // Handle Enter key press
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  // State for confirmation dialog
  const [showConfirm, setShowConfirm] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [confirmError, setConfirmError] = useState("");
  
  // Show confirmation dialog
  const promptCloseTicket = () => {
    setShowConfirm(true);
    setConfirmError("");
  };
  
  // Cancel confirmation
  const cancelCloseTicket = () => {
    setShowConfirm(false);
  };
  
  // Close ticket after confirmation
  const closeTicket = () => {
    setConfirmLoading(true);
    setConfirmError("");
    
    const token = getToken();
    
    if (!token) {
      setConfirmError('Authentication required. Please log in.');
      setConfirmLoading(false);
      return;
    }
    
    fetch("/api.php?f=close_ticket", {
      method: "POST",
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        ticket_id: ticketId,
        token
      })
    })
    .then(response => {
      // Handle 403 response
      if (response.status === 403) {
        setConfirmError('Access denied: You do not have permission to close this ticket');
        setConfirmLoading(false);
        return null;
      }
      
      return response.json();
    })
    .then(data => {
      setConfirmLoading(false);
      if (!data) return; // Handle 403 case
      
      if (data.success) {
        setShowConfirm(false);
        fetchTicketDetails();
        fetchMessages();
      } else {
        setConfirmError("Failed to close ticket: " + (data.message || "Unknown error"));
      }
    })
    .catch(error => {
      console.error("Error closing ticket:", error);
      setConfirmLoading(false);
      setConfirmError("Network error. Please try again.");
    });
  };

  // Initial load
  useEffect(() => {
    if (ticketId) {
      fetchTicketDetails();
      fetchMessages();
      
      // Refresh messages every 30 seconds (only if access is granted)
      const interval = setInterval(() => {
        if (hasAccess === true) {
          fetchMessages();
        }
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [ticketId, hasAccess]);

  // Chat messages component
  const ChatBody = () => {
    if (loading && messages.length === 0) {
      return (
        <div className="d-flex justify-content-center align-items-center p-5">
          <div className="spinner-border text-primary" role="status">
          </div>
        </div>
      );
    }
    
    // Ensure messages is an array
    const messageList = Array.isArray(messages) ? messages : [];
    
    if (messageList.length === 0 && !loading) {
      return (
        <div className="text-center p-5 text-muted">
          <i className="fa fa-comment-o fa-3x mb-3"></i>
          <p>No messages yet</p>
        </div>
      );
    }
    
    return (
      <>
        {messageList.map((message, index) => (
          <div key={message.id || index} className={message.type === 'zet' ? "media mt-1" : "media flex-row-reverse"}>
            <div className="media-body">
            <div className="main-msg-wrapper">
  {message.message.split('\n').map((line, i) => (
    <React.Fragment key={i}>
      {line}
      {i !== message.message.split('\n').length - 1 && <br />}
    </React.Fragment>
  ))}
</div>
              <div>
                <span>{message.time}</span>
              </div>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </>
    );
  };

  // Show loading state
  if (hasAccess === null) {
    return (
      <>
        <div className="page-header">
          <div className="page-leftheader">
            <ol className="breadcrumb">
              <li className="breadcrumb-item1"><Link to="/support">Support</Link></li>
              <li className="breadcrumb-item1">Ticket #{ticketId}</li>
            </ol>
          </div>
        </div>
        
        <div className="row">
          <div className="col-12">
            <div className="card noheight">
              <div className="card-header">
                <div className="card-title">Help Desk</div>
              </div>
              <div className="card-body">
                <div className="d-flex justify-content-center p-5">
                  <div className="spinner-border text-primary" role="status">
               
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  // Show no access message
  if (hasAccess === false) {
    return (
      <>
        <div className="page-header">
          <div className="page-leftheader">
            <ol className="breadcrumb">
              <li className="breadcrumb-item1"><Link to="/support">Support</Link></li>
              <li className="breadcrumb-item1">Ticket #{ticketId}</li>
            </ol>
          </div>
        </div>
        
        <div className="row">
          <div className="col-12">
            <div className="card noheight">
              <div className="card-header">
                <div className="card-title">Help Desk</div>
              </div>
              <div className="card-body">
                <NoAccessMessage />
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <div className="page-header">
        <div className="page-leftheader">
          <ol className="breadcrumb">
            <li className="breadcrumb-item1"><Link to="/support">Support</Link></li>
            <li className="breadcrumb-item1">Ticket #{ticketId}</li>
          </ol>
        </div>
        <div className="page-rightheader ml-auto">
          <div className="dropdown">
            <a href="#" className="nav-link pr-0 leading-none" data-toggle="dropdown">
              <button className="btn btn-success">New Service</button>
            </a>
            <div className="dropdown-menu">
              <Link className="dropdown-item d-flex" to="/dedicatedorder">
                <div className="mt-1">Dedicated Server</div>
              </Link>
              <Link className="dropdown-item d-flex" to="/cloudorder">
                <div className="mt-1">Cloud Server</div>
              </Link>
            </div>
          </div>
        </div>
      </div>
      <div className="row">
        <div className="col-12">
          <DiscountAlert />

          <div className="card noheight">
            <div className="card-header">
              <div className="card-title">Help Desk</div>
              <div className="card-options">
                {ticket && ticket.status !== 'Closed' && (
                  <button onClick={promptCloseTicket} className="btn btn-danger btn-sm mr-2">
                    Close Ticket
                  </button>
                )}
              </div>
            </div>
            <div className="row no-gutters">
              <div className="col-xl-12 col-lg-12">
                <div className="border-left">
                  <div className="main-content-body main-content-body-chat">
                    <div className="main-chat-header p-3">
                      <div className="main-chat-msg-name">
       
                        {ticket && (
                          <>
                            <div className="d-flex align-items-center mb-1">
                              <h4 className="mb-0">{ticket.subject}</h4>
                              <span className={`badge ml-2 bg-${ticket.status === 'Open' ? 'primary' : ticket.status === 'Closed' ? 'success' : 'warning'}`}>
                                {ticket.status}
                              </span>
                            </div>
                            <small>Department: {ticket.department} | Priority: {ticket.priority || 'Low'}</small><br />
                            <small>Created: {ticket.created_date} | Last update: {ticket.last_reply_date}</small>
                          </>
                        )}
                      </div>
                    </div>
                    <div className="main-chat-body" id="ChatBody">
                      <div className="content-inner">
                        <ChatBody />
                      </div>
                    </div>
                    <div className="main-chat-footer">
                      <input 
                        className="form-control" 
                        placeholder="Type your message here..." 
                        type="text" 
                        ref={messageInputRef}
                        onKeyPress={handleKeyPress}
                        disabled={sending || (ticket && ticket.status === 'Closed')}
                      /> 
                      <a 
                        className="main-msg-send" 
                        onClick={sendMessage}
                        style={{ cursor: sending || (ticket && ticket.status === 'Closed') ? 'not-allowed' : 'pointer' }}
                      >
                        {sending ? (
                          <div className="spinner-border spinner-border-sm text-primary" role="status">
                            <span className="sr-only">Sending...</span>
                          </div>
                        ) : (
                          <svg className="svg-icon" xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24">
                            <path d="M0 0h24v24H0V0z" fill="none"/>
                            <path d="M4 8.25l7.51 1-7.5-3.22zm.01 9.72l7.5-3.22-7.51 1z" opacity=".3"/>
                            <path d="M2.01 3L2 10l15 2-15 2 .01 7L23 12 2.01 3zM4 8.25V6.03l7.51 3.22-7.51-1zm.01 9.72v-2.22l7.51-1-7.51 3.22z"/>
                          </svg>
                        )}
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Confirmation Modal */}
      {showConfirm && (
        <div className="modal d-block" tabIndex="-1" role="dialog" style={{backgroundColor: 'rgba(0,0,0,0.5)'}}>
          <div className="modal-dialog" role="document">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Confirm Close Ticket</h5>
                <button type="button" className="close" onClick={cancelCloseTicket}>
                  <span aria-hidden="true">&times;</span>
                </button>
              </div>
              <div className="modal-body">
                <p>Are you sure you want to close this ticket? This action cannot be undone.</p>
                {confirmError && (
                  <div className="alert alert-danger mt-3">
                    {confirmError}
                  </div>
                )}
              </div>
              <div className="modal-footer">
                <button 
                  type="button" 
                  className="btn btn-secondary" 
                  onClick={cancelCloseTicket}
                  disabled={confirmLoading}
                >
                  Cancel
                </button>
                <button 
                  type="button" 
                  className="btn btn-danger" 
                  onClick={closeTicket}
                  disabled={confirmLoading}
                >
                  {confirmLoading ? (
                    <>
                      <span className="spinner-border spinner-border-sm mr-2" role="status" aria-hidden="true"></span>
                      Closing...
                    </>
                  ) : 'Close Ticket'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Notification Toast */}
      {notification.show && (
        <div 
          className={`toast show position-fixed`} 
          style={{top: '20px', right: '20px', zIndex: 9999}}
          role="alert" 
          aria-live="assertive" 
          aria-atomic="true"
        >
          <div className={`toast-header bg-${notification.type === 'error' ? 'danger' : 'success'} text-white`}>
            <strong className="mr-auto">
              {notification.type === 'error' ? 'Error' : 'Success'}
            </strong>
            <button 
              type="button" 
              className="ml-2 mb-1 close text-white" 
              onClick={() => setNotification({...notification, show: false})}
            >
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div className="toast-body">
            {notification.message}
          </div>
        </div>
      )}
    </>
  );
};

export default Ticket;