import React, { useState, useEffect, useCallback } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Card, CardBody, Row, Col, Container, Al<PERSON>, Modal, ModalHeader, <PERSON>dal<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Spin<PERSON> } from 'reactstrap';
import PaymentModal from './PaymentModal.js';
import { Alert as BootstrapAlert } from "react-bootstrap";
import DiscountAlert from "../components/DiscountAlert";

const CloudOrder = () => {
  // State for configurations
  const [modelOptions, setModelOptions] = useState([]);
  const [locationOptions, setLocationOptions] = useState([]);
  const [osOptions, setOsOptions] = useState([]);
  const [ipOptions, setIpOptions] = useState([]);

  // State for selections
  const [selectedModel, setSelectedModel] = useState(null);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [selectedOS, setSelectedOS] = useState(null);
  const [selectedIP, setSelectedIP] = useState(null);
  const [serversNumber, setServersNumber] = useState(1);
  const [termsAccepted, setTermsAccepted] = useState(false);
  
  // State for UI
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [totalPrice, setTotalPrice] = useState(0);
  const [deliveryTime, setDeliveryTime] = useState('INSTANT');
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [orderSuccess, setOrderSuccess] = useState(false);
  const [orderId, setOrderId] = useState(null);
  const [invoiceId, setInvoiceId] = useState(null);
  const [generatingInvoice, setGeneratingInvoice] = useState(false);
  
  // State for VAT calculation
  const [vatRate, setVatRate] = useState(0);
  const [vatAmount, setVatAmount] = useState(0);
  const [totalWithVat, setTotalWithVat] = useState(0);
  
  // Temporary state for modal selections
  const [tempServersNumber, setTempServersNumber] = useState(1);
  const [modalTotalPrice, setModalTotalPrice] = useState(0);

  const navigate = useNavigate();

  // Function to get token from session storage
  const getToken = useCallback(() => {
    return sessionStorage.getItem('token');
  }, []);
  
  // API Base URL
  const API_BASE_URL = '/api.php';

  // Fetch configuration data from the API
  const fetchCloudOrderConfig = useCallback(async (params = {}) => {
    setLoading(true);
    setError(null);
    
    const token = getToken();
    if (!token) {
      setError('Authentication token not found. Please log in again.');
      setLoading(false);
      return;
    }
    
    // Construct the query string with any parameters
    const queryParams = new URLSearchParams({
      f: 'cloudorder',
      ...params
    }).toString();
    
    try {
      console.log(`Fetching config with params: ${queryParams}`);
      
      const response = await fetch(`${API_BASE_URL}?${queryParams}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      
      // Get the raw text first to inspect it
      const rawText = await response.text();
      console.log('Raw API response:', rawText);
      
      // Try to parse the response as JSON
      let data;
      try {
        data = JSON.parse(rawText);
      } catch (parseError) {
        console.error('JSON parse error:', parseError);
        throw new Error(`Invalid JSON response: ${rawText.substring(0, 100)}...`);
      }
      
      if (data.error) {
        if (data.error === 5) {
          alert('ERROR: Your login session has timed out');
          window.location.reload(false);
        } else {
          setError(`ERROR: ${data.error}`);
        }
      } else {
        if (Array.isArray(data) && data.length > 0) {
          processConfigData(data[0]);
        } else {
          throw new Error('Invalid data structure received from API');
        }
      }
    } catch (err) {
      console.error('Fetch error:', err);
      setError(`Failed to fetch configuration data: ${err.message}`);
    } finally {
      setLoading(false);
    }
  }, [getToken]);

  // Process the configuration data
  const processConfigData = useCallback((data) => {
    if (!data) {
      console.error("No configuration data received");
      return;
    }
    
    console.log("Processing config data:", data);
    
    // Set options and initial selections (make sure to reset arrays to prevent duplication)
    if (Array.isArray(data.models)) {
      console.log(`Setting ${data.models.length} model options`);
      setModelOptions([...data.models]);
    } else {
      console.warn("No model options received");
      setModelOptions([]);
    }
    
    if (Array.isArray(data.location)) {
      console.log(`Setting ${data.location.length} location options`);
      setLocationOptions([...data.location]);
    } else {
      console.warn("No location options received");
      setLocationOptions([]);
    }
    
    if (Array.isArray(data.ip)) {
      console.log(`Setting ${data.ip.length} IP options`);
      setIpOptions([...data.ip]);
    } else {
      console.warn("No IP options received");
      setIpOptions([]);
    }
    
    if (Array.isArray(data.os)) {
      console.log(`Setting ${data.os.length} OS options`);
      setOsOptions([...data.os]);
    } else {
      console.warn("No OS options received");
      setOsOptions([]);
    }
    
    // Find selected options
    const initialModel = data.models?.find(model => model.checked);
    const initialLocation = data.location?.find(location => location.checked);
    const initialOS = data.os?.find(os => os.checked);
    const initialIP = data.ip?.find(ip => ip.checked);
    
    console.log("Initial selections:", {
      model: initialModel?.id,
      location: initialLocation?.id,
      os: initialOS?.id,
      ip: initialIP?.id
    });
    
    // Set selections
    if (initialModel) setSelectedModel(initialModel);
    if (initialLocation) setSelectedLocation(initialLocation);
    if (initialOS) setSelectedOS(initialOS);
    if (initialIP) setSelectedIP(initialIP);
    
    // Set price
    if (data.price !== undefined) setTotalPrice(data.price);
    
    // Set delivery time (usually INSTANT for cloud servers)
    if (data.delivery) setDeliveryTime(data.delivery);
    
    // Also update temp selections to match initial values
    setTempServersNumber(serversNumber);
    if (data.price !== undefined) setModalTotalPrice(data.price);
  }, [serversNumber]);

  // Fetch initial configuration on component mount
  useEffect(() => {
    fetchCloudOrderConfig();
  }, [fetchCloudOrderConfig]);

  // Update price based on selections
  useEffect(() => {
    if (selectedModel && selectedIP) {
      let price = Number(selectedModel.price || 0);
      
      // Add price for IP if applicable
      if (selectedIP.price) {
        price += Number(selectedIP.price);
      }
      
      // Calculate for multiple servers
      price = price * serversNumber;
      
      setTotalPrice(price);
      setModalTotalPrice(price);
    }
  }, [selectedModel, selectedIP, serversNumber]);

  // Update selections for a specific category
  const updateSelection = useCallback(async (category, selection) => {
    console.log(`Updating ${category} selection to ID: ${selection.id}`);
    
    // Create a copy of the current state for API parameters
    const params = {};
    
    // Add all current selections to params (if they exist)
    if (selectedModel) params.model_id = selectedModel.id;
    if (selectedLocation) params.location_id = selectedLocation.id;
    if (selectedOS) params.os_id = selectedOS.id;
    if (selectedIP) params.ip_id = selectedIP.id;
    
    // Override with the new selection
    params[`${category}_id`] = selection.id;
    
    console.log('Selection params:', params);
    
    // Update the local state first for responsive UI
    switch (category) {
      case 'model':
        setSelectedModel(selection);
        break;
      case 'location':
        setSelectedLocation(selection);
        break;
      case 'os':
        setSelectedOS(selection);
        break;
      case 'ip':
        setSelectedIP(selection);
        break;
      default:
        break;
    }
    
    // Fetch updated configuration
    await fetchCloudOrderConfig(params);
  }, [selectedModel, selectedLocation, selectedOS, selectedIP, fetchCloudOrderConfig]);

  // Event handlers for selection changes
  const handleModelChange = model => updateSelection('model', model);
  const handleLocationChange = location => updateSelection('location', location);
  const handleOSChange = os => updateSelection('os', os);
  const handleIPChange = ip => updateSelection('ip', ip);

  // Handle servers number change
  const handleServersNumberChange = (number) => {
    setServersNumber(number);
  };

  // Toggle confirmation modal
  const toggleConfirmModal = () => {
    if (showConfirmModal) {
      // When closing the modal, reset temporary values
      setTempServersNumber(serversNumber);
    } else {
      // When opening the modal, initialize temp values with current selections
      setTempServersNumber(serversNumber);
      
      // Initialize modal price with current total price
      setModalTotalPrice(totalPrice);
    }
    setShowConfirmModal(!showConfirmModal);
  };

  // Toggle payment modal
  const togglePaymentModal = () => {
    setShowPaymentModal(!showPaymentModal);
  };

  // Handle order button click
  const handleOrderClick = () => {
    // Validate required selections
    if (!selectedModel || !selectedLocation || !selectedOS || !selectedIP) {
      setError('Please select all required options before ordering.');
      return;
    }
    
    // Open the confirmation modal
    toggleConfirmModal();
  };
  
  // Handle temporary servers number change (only within modal)
  const handleTempServersNumberChange = (number) => {
    setTempServersNumber(number);
    
    // Update modal price when servers number changes
    if (selectedModel && selectedIP) {
      let price = Number(selectedModel.price || 0);
      
      // Add price for IP if applicable
      if (selectedIP.price) {
        price += Number(selectedIP.price);
      }
      
      // Calculate for multiple servers
      price = price * number;
      
      setModalTotalPrice(price);
    }
  };

// Generate invoice for the order
const generateInvoice = async (orderId) => {
  setGeneratingInvoice(true);
  
  try {
    console.log(`Generating invoice for order ID: ${orderId}, amount: ${modalTotalPrice}`);
    console.log(`VAT information: Rate=${vatRate}%, Amount=${vatAmount}, Total with VAT=${totalWithVat}`);
    
    const token = getToken();
    
    // Call the generate_invoice API endpoint
    const response = await fetch(`${API_BASE_URL}?f=generate_invoice`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token,
        order_id: orderId,
        amount: modalTotalPrice,
        type: 'Cloud Server',
        vat_rate: vatRate,
        vat_amount: vatAmount,
        total_with_vat: totalWithVat,
        items_created: true  // Flag to indicate items are already created
      })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    
    const data = await response.json();
    console.log("Invoice generation response:", data);
    
    if (data.success && data.invoice_id) {
      console.log(`Invoice generated: ${data.invoice_id}`);
      setInvoiceId(data.invoice_id);
      return data.invoice_id;
    } else {
      console.warn("Failed to generate invoice:", data.error || "Unknown error");
      // Create a fallback invoice ID
      const fallbackId = `INV${Date.now().toString().slice(-6)}`;
      setInvoiceId(fallbackId);
      return fallbackId;
    }
  } catch (err) {
    console.error("Error generating invoice:", err);
    // Create a fallback invoice ID
    const fallbackId = `INV${Date.now().toString().slice(-6)}`;
    setInvoiceId(fallbackId);
    return fallbackId;
  } finally {
    setGeneratingInvoice(false);
  }
};

  // Place cloud order with the API
// Place cloud order with the API
const placeCloudOrder = async () => {
  setLoading(true);
  setError(null);
  
  const token = getToken();
  if (!token) {
    setError('Authentication token not found. Please log in again.');
    setLoading(false);
    return;
  }
  
  try {
    // Detailed logging of configuration
    console.group('Cloud Server Order Configuration');
    console.log('User Token:', token);
    console.log('Model:', selectedModel);
    console.log('Location:', selectedLocation);
    console.log('OS:', selectedOS);
    console.log('IP:', selectedIP);
    console.log('Servers Number:', tempServersNumber);
    console.log('Total Price:', modalTotalPrice);
    console.groupEnd();
    
    // Validate required selections
    if (!selectedModel || !selectedLocation || !selectedOS || !selectedIP) {
      throw new Error('Please ensure all configuration options are selected.');
    }
    
    // Prepare order parameters
    const orderParams = new URLSearchParams({
      f: 'placecloudorder',
      model_id: selectedModel.id,
      location_id: selectedLocation.id,
      os_id: selectedOS.id,
      ip_id: selectedIP.id,
      servers_number: tempServersNumber
    }).toString();
    
    console.log('Order Parameters:', orderParams);
    
    // Make the API request
    const response = await fetch(`${API_BASE_URL}?${orderParams}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ token })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    
    // Get the response text and try to parse it
    const rawText = await response.text();
    console.log('Raw API response:', rawText);
    
    let data;
    try {
      data = JSON.parse(rawText);
    } catch (parseError) {
      console.error('JSON parse error:', parseError);
      throw new Error(`Invalid JSON response: ${rawText.substring(0, 100)}...`);
    }
    
    // Check for API error
    if (!data.success) {
      throw new Error(`Order placement failed: ${data.error || 'Unknown error'}`);
    }
    
    // Extract order ID
    const orderId = data.orderid;
    if (!orderId) {
      throw new Error("Server did not return a valid order ID");
    }
    
    setOrderId(orderId);
    
    // Handle VAT information if available in the response
    if (data.vat_rate !== undefined) {
      // Update state variables with VAT information
      const newVatRate = data.vat_rate;
      const newVatAmount = data.vat_amount || 0;
      const newTotalWithVat = data.total_with_vat || modalTotalPrice;
      
      setVatRate(newVatRate);
      setVatAmount(newVatAmount);
      setTotalWithVat(newTotalWithVat);
      
      console.log(`VAT information received: Rate=${newVatRate}%, Amount=${newVatAmount}, Total=${newTotalWithVat}`);
    }
    
    // Check if invoice ID was already returned from the backend
    if (data.invoice_id) {
      console.log(`Invoice already created with ID: ${data.invoice_id}`);
      setInvoiceId(data.invoice_id);
    } else {
      // Only try to generate an invoice if one wasn't created during order placement
      try {
        console.group('Invoice Generation');
        console.log(`Generating invoice for Order ID: ${orderId}`);
        console.log(`Invoice Amount: ${modalTotalPrice}`);
        
        await generateInvoice(orderId);
        console.groupEnd();
      } catch (invoiceError) {
        console.error('Invoice Generation Error:', invoiceError);
        // Continue with order process even if invoice generation fails
      }
    }
    
    // Order successfully placed
    setOrderSuccess(true);
    
    // Close confirmation modal
    toggleConfirmModal();
    
    // Open payment modal
    setTimeout(() => {
      togglePaymentModal();
    }, 100);
    
  } catch (err) {
    console.error('Order Placement Error:', err);
    
    // Detailed error logging
    console.log('Error Details:', {
      message: err.message,
      stack: err.stack
    });
    
    setError(err.message || 'An unexpected error occurred while placing your order');
  } finally {
    setLoading(false);
  }
};

  // Handle order submission
  const handleOrder = () => {
    if (!termsAccepted) {
      setError('Please accept the Terms of Service before proceeding.');
      return;
    }
    
    // Apply the temporary selections to the actual state
    setServersNumber(tempServersNumber);
    
    // Place the order
    placeCloudOrder();
  };

  // Handle payment completion
  const handlePaymentComplete = () => {
    // Close the payment modal
    togglePaymentModal();
    
    // Navigate to the invoice page if we have an invoice ID
    if (invoiceId) {
      navigate(`/billing/invoices/${invoiceId}`);
    } else {
      // Otherwise navigate to the server list/dashboard
      navigate('/');
    }
  };

  // Render loading spinner
  if (loading && !selectedModel) {
    return (
      <Container fluid className="p-0">
        <div className="page-header">
          <div className="page-leftheader">
            <ol className="breadcrumb">
              <li className="breadcrumb-item1"><Link to="/">Home</Link></li>
              <li className="breadcrumb-item1">New Cloud Server</li>
            </ol>
          </div>
        </div>
        <div className="text-center my-5 py-5">
          <Spinner color="primary" />
        </div>
      </Container>
    );
  }

  // Render error state
  if (error && !selectedModel) {
    return (
      <Container fluid className="p-0">
        <div className="page-header">
          <div className="page-leftheader">
            <ol className="breadcrumb">
              <li className="breadcrumb-item1"><Link to="/">Home</Link></li>
              <li className="breadcrumb-item1">New Cloud Server</li>
            </ol>
          </div>
        </div>
        <Alert color="danger" className="my-4">
          <i className="fa fa-exclamation-circle me-2"></i>
          <strong>Error:</strong> {error}
          <div className="mt-3">
            <Button color="primary" onClick={() => fetchCloudOrderConfig()}>
              Try Again
            </Button>
          </div>
        </Alert>
      </Container>
    );
  }

  return (
    <Container fluid className="p-0">
      {/* Page Header */}
      <div className="page-header">
        <div className="page-leftheader">
          <ol className="breadcrumb">
            <li className="breadcrumb-item1"><Link to="/">Home</Link></li>
            <li className="breadcrumb-item1">New Cloud Server</li>
          </ol>
        </div>
        <div className="page-rightheader ml-auto">
          <div className="dropdown">
            <a href="#" className="nav-link pr-0 leading-none" data-toggle="dropdown">
              <button className="btn btn-success">New Service</button>
            </a>
            <div className="dropdown-menu">
              <Link className="dropdown-item d-flex" to="/dedicatedorder">
                <div className="mt-1">Dedicated Server</div>
              </Link>
              <Link className="dropdown-item d-flex" to="/cloudorder">
                <div className="mt-1">Cloud Server</div>
              </Link>

            </div>
          </div>
        </div>
      </div>

      {/* Success Alert */}
      <DiscountAlert />

      {/* Error Alert */}
      {error && (
        <BootstrapAlert variant="danger" className="mb-4">
          <i className="fa fa-exclamation-circle me-2"></i>
          <strong>Error:</strong> {error}
        </BootstrapAlert>
      )}

      {/* Configuration Card */}
      <Card className="shadow-sm mb-4">
        <CardBody>
          <h2 className="card-title text-primary mb-4">Configure Your Cloud Server</h2>
          
          <Row className="g-4 mb-4">
            {/* Server Model Selection */}
            <Col md={12} lg>
              <Card className="h-100 border">
                <div className="card-header bg-light">
                  <h5 className="mb-0">
                    <i className="fa fa-server text-primary me-2" style={{paddingRight: '0.5rem'}}></i>
                    Server Model
                  </h5>
                </div>
                <CardBody className="p-0">
                  {modelOptions.length > 0 ? (
                    <div className="better-radio-group" style={{ overflowY: 'auto' }}>
                      {modelOptions.map(model => (
                        <div key={model.id} className="position-relative">
                          <input
                            type="radio"
                            id={`model_${model.id}`}
                            name="serverModel"
                            className="better-radio"
                            checked={selectedModel && selectedModel.id === model.id}
                            onChange={() => handleModelChange(model)}
                          />
                          <label 
                            htmlFor={`model_${model.id}`} 
                            className={`list-group-item d-flex flex-column p-3 ${selectedModel && selectedModel.id === model.id ? 'selected' : ''}`}
                          >
                            <div className="fw-bold">{model.cpu}</div>
                            <div className="text-muted small">{model.ram}</div>
                            <div className="text-muted small">{model.disk}</div>
                            <div className="text-success">
                              {model.price > 0 ? `€${model.price}` : 'Included'}
                            </div>
                          </label>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="p-3 text-center text-muted">
                      <i className="fa fa-info-circle me-2"></i>
                      No server models available
                    </div>
                  )}
                </CardBody>
              </Card>
            </Col>

            {/* Location Selection */}
            <Col md={12} lg>
              <Card className="h-100 border">
                <div className="card-header bg-light">
                  <h5 className="mb-0">
                    <i className="fa fa-map-marker text-primary me-2" style={{paddingRight: '0.5rem'}}></i>
                    Location
                  </h5>
                </div>
                <CardBody className="p-0">
                  {locationOptions.length > 0 ? (
                    <div className="better-radio-group" style={{ overflowY: 'auto' }}>
                      {locationOptions.map(location => (
                        <div key={location.id} className="position-relative">
                          <input
                            type="radio"
                            id={`location_${location.id}`}
                            name="location"
                            className="better-radio"
                            checked={selectedLocation && selectedLocation.id === location.id}
                            onChange={() => handleLocationChange(location)}
                          />
                          <label 
                            htmlFor={`location_${location.id}`} 
                            className={`list-group-item d-flex align-items-center p-3 ${selectedLocation && selectedLocation.id === location.id ? 'selected' : ''}`}
                          >
                            {location.flag && (
                              <i className={`flag flag-${location.flag.toLowerCase()}`} style={{ marginRight: '10px' }}></i>
                            )}
                            <div>
                              <div className="fw-bold">{location.name}</div>
                              <div className="text-success">
                                {location.price > 0 ? `+€${location.price}` : 'Included'}
                              </div>
                            </div>
                          </label>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="p-3 text-center text-muted">
                      <i className="fa fa-info-circle me-2"></i>
                      No locations available
                    </div>
                  )}
                </CardBody>
              </Card>
            </Col>

            {/* IP Selection */}
            <Col md={12} lg>
              <Card className="h-100 border">
                <div className="card-header bg-light">
                  <h5 className="mb-0">
                    <i className="fa fa-globe me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>
                    IPv4 Number
                  </h5>
                </div>
                <CardBody className="p-0">
                  {ipOptions.length > 0 ? (
                    <div className="better-radio-group" style={{ overflowY: 'auto' }}>
                      {ipOptions.map(ip => (
                        <div key={ip.id} className="position-relative">
                          <input
                            type="radio"
                            id={`ip_${ip.id}`}
                            name="ipNumber"
                            className="better-radio"
                            checked={selectedIP && selectedIP.id === ip.id}
                            onChange={() => handleIPChange(ip)}
                          />
                          <label 
                            htmlFor={`ip_${ip.id}`} 
                            className={`list-group-item d-flex flex-column p-3 ${selectedIP && selectedIP.id === ip.id ? 'selected' : ''}`}
                          >
                            <div className="fw-bold">{ip.name}</div>
                            <div className="text-muted small">
                              {ip.price > 0 ? `+€${ip.price}` : 'Included'}
                            </div>
                          </label>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="p-3 text-center text-muted">
                      <i className="fa fa-info-circle me-2"></i>
                      No IP options available
                    </div>
                  )}
                </CardBody>
              </Card>
            </Col>

            {/* Operating System Selection */}
            <Col md={12} lg>
              <Card className="h-100 border">
                <div className="card-header bg-light">
                  <h5 className="mb-0">
                    <i className="fa fa-desktop text-primary me-2" style={{paddingRight: '0.5rem'}}></i>
                    Operating System
                  </h5>
                </div>
                <CardBody className="p-0">
                  {osOptions.length > 0 ? (
                    <div className="better-radio-group" style={{ maxHeight: '579px', overflowY: 'auto' }}>
                      {osOptions.map(os => (
                        <div key={os.id} className="position-relative">
                          <input
                            type="radio"
                            id={`os_${os.id}`}
                            name="osType"
                            className="better-radio"
                            checked={selectedOS && selectedOS.id === os.id}
                            onChange={() => handleOSChange(os)}
                          />
                          <label 
                            htmlFor={`os_${os.id}`} 
                            className={`list-group-item d-flex align-items-center p-3 ${selectedOS && selectedOS.id === os.id ? 'selected' : ''}`}
                          >
                            <img 
                              src={`/assets/images/os/${os.name.split(' ')[0].toLowerCase()}.png`} 
                              alt={os.name} 
                              style={{width: '24px', height: '24px', marginRight: '10px'}} 
                            />
                            <div>
                              <div className="fw-bold">{os.name}</div>
                              <div className="text-success">
                                {os.price > 0 ? `+€${os.price}` : 'Included'}
                              </div>
                            </div>
                          </label>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="p-3 text-center text-muted">
                      <i className="fa fa-info-circle me-2"></i>
                      No operating systems available
                    </div>
                  )}
                </CardBody>
              </Card>
            </Col>
          </Row>

          {/* Order Summary and Action Buttons */}
          <Card className="border-0 bg-light shadow-sm p-4">
            <Row className="align-items-center">
              <Col xs={12} md={6}>
                <h3 className="mb-0">Total Price: <span className="text-success">€{totalPrice}/mo</span></h3>
                <p className="text-muted mb-0">Delivery: {deliveryTime}</p>
              </Col>
              <Col xs={12} md={6} className="text-md-end mt-3 mt-md-0 d-flex justify-content-end">
                <Button className="btn btn-success" size="lg" onClick={handleOrderClick} disabled={loading}>
                  {loading ? (
                    <>
                      <Spinner size="sm" className="me-2" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <i className="fa fa-check-circle me-2" style={{paddingRight: '0.5rem'}}></i>
                      Order Now
                    </>
                  )}
                </Button>
              </Col>
            </Row>
          </Card>
        </CardBody>
      </Card>

      {/* Order Confirmation Modal */}
      <Modal 
        isOpen={showConfirmModal} 
        toggle={toggleConfirmModal}
        className="order-summary-modal"
        backdropClassName="fixed-backdrop"
        backdrop={true}
        zIndex={9999}
        size="lg"
      >
        <ModalHeader toggle={toggleConfirmModal}>Order Summary</ModalHeader>
        <ModalBody>
          {error && (
            <BootstrapAlert variant="danger" className="mb-4">
              <i className="fa fa-exclamation-circle me-2"></i>
              <strong>Error:</strong> {error}
            </BootstrapAlert>
          )}
          
          {selectedModel && selectedLocation && selectedOS && selectedIP && (
            <>
              <h5 className="mb-3">Selected Configuration</h5>
              <div className="table-responsive mb-4">
                <table className="table card-table table-vcenter text-nowrap">
                  <tbody>
                    <tr>
                      <td><b>Server CPU</b></td>
                      <td>{selectedModel.cpu}</td>
                    </tr>
                    <tr>
                      <td><b>Server RAM</b></td>
                      <td>{selectedModel.ram}</td>
                    </tr>
                    <tr>
                      <td><b>Storage</b></td>
                      <td>{selectedModel.disk}</td>
                    </tr>
                    <tr>
                      <td><b>Location</b></td>
                      <td>
                        {selectedLocation.flag && (
                          <i className={`flag flag-${selectedLocation.flag.toLowerCase()}`} style={{ marginRight: '5px' }}></i>
                        )}
                        {selectedLocation.name}
                      </td>
                    </tr>
                    <tr>
                      <td><b>IP Addresses</b></td>
                      <td>{selectedIP.name}</td>
                    </tr>
                    <tr>
                      <td><b>Servers Number</b></td>
                      <td>
                        <select 
                          className="form-select form-control" 
                          value={tempServersNumber} 
                          onChange={(e) => handleTempServersNumberChange(parseInt(e.target.value))}
                        >
                          {[1, 2, 3, 4, 5, 10, 20, 50, 100].map(num => (
                            <option key={num} value={num}>
                              {num} {num === 1 ? 'Server' : 'Servers'}
                            </option>
                          ))}
                        </select>
                      </td>
                    </tr>
                    <tr>
                      <td><b>Operating System</b></td>
                      <td>
                        <img 
                          src={`/assets/images/os/${selectedOS.name.split(' ')[0].toLowerCase()}.png`} 
                          alt={selectedOS.name} 
                          style={{width: '24px', height: '24px', marginRight: '10px'}} 
                        />
                        {selectedOS.name}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              
              <div className="card border-0 bg-light shadow-sm p-3 mb-3">
                <div className="d-flex justify-content-between align-items-center">
                  <div>
                    <h4 className="mb-0">Total Price: <span className="text-success">€{modalTotalPrice}/mo</span></h4>
                    <p className="text-muted mb-0">Delivery: {deliveryTime}</p>
                  </div>
                </div>
              </div>
            </>
          )}
        </ModalBody>
        <ModalFooter className="d-flex flex-column p-3">
          {/* Top row with terms of service */}
          <div className="w-100 mb-3">
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <input
                type="checkbox"
                id="agreeTerms"
                checked={termsAccepted}
                onChange={() => setTermsAccepted(!termsAccepted)}
                style={{ 
                  marginRight: '5px',
                  marginLeft: '0',
                  marginTop: '0',
                  marginBottom: '0',
                  padding: '0',
                  position: 'relative',
                  float: 'none'
                }}
                required
              />
              I accept the <a href="#" target="_blank" style={{ marginLeft: '3px', color: '#1e88e5' }}>Terms of Service</a>
            </div>
          </div>
          
          {/* Bottom row with buttons */}
          <div className="d-flex justify-content-between w-100">
            <Button color="secondary" onClick={toggleConfirmModal} disabled={loading}>Cancel</Button>
            <Button 
              color="success" 
              onClick={handleOrder} 
              disabled={!termsAccepted || loading}
            >
              {loading ? (
                <>
                  <Spinner size="sm" className="me-2" />
                  Processing...
                </>
              ) : (
                'Confirm Order'
              )}
            </Button>
          </div>
        </ModalFooter>
      </Modal>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={showPaymentModal}
        toggle={togglePaymentModal}
        totalAmount={modalTotalPrice}
        onComplete={handlePaymentComplete}
        orderId={orderId}
        invoiceId={invoiceId}
        generatingInvoice={generatingInvoice}
        vatRate={vatRate}
        vatAmount={vatAmount}
        totalWithVat={totalWithVat}
      />


    </Container>
  );
};

export default CloudOrder;