import React, { useState, useEffect, useCallback } from "react";
import { Link, useParams, useNavigate } from "react-router-dom";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, AreaChart, Area } from 'recharts';
import {
  MDBModal,
  MDBModalDialog,
  MDBModalContent,
  MDBModalHeader,
  MDBModalTitle,
  MDBModalBody,
  MDBModalFooter,
} from 'mdb-react-ui-kit';
import VncModal from '../components/VncModal';
import DiscountAlert from "../components/DiscountAlert";
import VpsUpgradeModal from '../components/VpsUpgradeModal';
import VpsIpUpgradeModal from '../components/VpsIpUpgradeModal';
const VpsServerDetailsView = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [server, setServer] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [powerStatus, setPowerStatus] = useState('unknown');
  const [powerLoading, setPowerLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [modalBody, setModalBody] = useState('');
  const [modalFooter, setModalFooter] = useState('');
  const [usageData, setUsageData] = useState([]);
  const [powerActionLoading, setPowerActionLoading] = useState(false);
  const [isUpdatingAutoRenewal, setIsUpdatingAutoRenewal] = useState(false);
  const [isUpdatingUseCredit, setIsUpdatingUseCredit] = useState(false);
  const [isUpdatingBillingCycle, setIsUpdatingBillingCycle] = useState(false);
  const [powerActionResult, setPowerActionResult] = useState(null);
  const [trafficPeriod, setTrafficPeriod] = useState('24h');
  const [loadingTraffic, setLoadingTraffic] = useState(false);
  const [loadingVnc, setLoadingVnc] = useState(false);
  const [showVncModal, setShowVncModal] = useState(false);
  const [vncDetails, setVncDetails] = useState(null);
  const [availableOSOptions, setAvailableOSOptions] = useState([]);
  const [selectedOS, setSelectedOS] = useState('');
  const [reinstallLoading, setReinstallLoading] = useState(false);
  const [reinstallResult, setReinstallResult] = useState(null);
  // Removed terminateConfirmation state as it's no longer needed
  const [terminateLoading, setTerminateLoading] = useState(false);
  const [terminateResult, setTerminateResult] = useState(null);
  const [resetPasswordLoading, setResetPasswordLoading] = useState(false);
  const [resetPasswordResult, setResetPasswordResult] = useState(null);
  const [newHostname, setNewHostname] = useState('');
  const [hostnameUpdateLoading, setHostnameUpdateLoading] = useState(false);
  const [hostnameUpdateResult, setHostnameUpdateResult] = useState(null);
  const [selectedIpId, setSelectedIpId] = useState(null);
  const [networkStats, setNetworkStats] = useState({
    peakTraffic: 0,
    avgTraffic: 0,
    monthlyUsage: 0,
    packetsPerSec: 0
  });
  const [showVpsUpgradeModal, setShowVpsUpgradeModal] = useState(false);
  const toggleVpsUpgradeModal = () => setShowVpsUpgradeModal(!showVpsUpgradeModal);
  const [showVpsIpUpgradeModal, setShowVpsIpUpgradeModal] = useState(false);
  const toggleVpsIpUpgradeModal = () => setShowVpsIpUpgradeModal(!showVpsIpUpgradeModal);

  // Reverse DNS state variables
  const [rdnsRecords, setRdnsRecords] = useState([]);
  const [rdnsLoading, setRdnsLoading] = useState(false);
  const [selectedIp, setSelectedIp] = useState(null);

  const [rdnsDomain, setRdnsDomain] = useState('');
  const [rdnsUpdateLoading, setRdnsUpdateLoading] = useState(false);
  const [rdnsUpdateResult, setRdnsUpdateResult] = useState(null);

  // Modal toggle function
  const toggleModal = () => setShowModal(!showModal);
  const fetchAvailableOSOptions = useCallback(async () => {
  try {
    const token = sessionStorage.getItem('token');
    console.log(`Fetching OS options for server ID: ${id}`);

    setAvailableOSOptions([]); // Clear previous options

    const response = await fetch(`/api.php?f=vps_get_os_options&id=${id}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      },
      body: JSON.stringify({ token })
    });

    const data = await response.json();

    if (data.error === 0 && Array.isArray(data.os_options)) {
      console.log(`Loaded ${data.os_options.length} OS options from database`);

      if (data.os_options.length > 0) {
        setAvailableOSOptions(data.os_options);

        // Set default selected OS - prefer Ubuntu 22.04 if available, otherwise first option
        const ubuntu = data.os_options.find(os =>
          os.name.toLowerCase().includes('ubuntu') && os.name.includes('22.04')
        );

        if (ubuntu) {
          setSelectedOS(ubuntu.id);
        } else {
          setSelectedOS(data.os_options[0].id);
        }
      } else {
        console.error("No OS options available in database");
      }
    } else {
      console.error("Error fetching OS options:", data.message || "Invalid response format");
    }
  } catch (error) {
    console.error("Error fetching OS options:", error);
  }
}, [id]);

const handleVpsIpUpgradeSuccess = (data) => {
  console.log('VPS IP upgrade invoice generated successfully:', data);
  
  // Suggest to the user to check their invoices
  setTimeout(() => {
    const goToInvoices = window.confirm("Would you like to view your invoices to complete the payment?");
    if (goToInvoices) {
      window.location.href = `/billing/invoices/`;
    }
  }, 500);
  
  // Refresh server details to get updated information
  setTimeout(() => {
    fetchServerDetails();
  }, 2000);
};

// Update the handleReinstallOS function
const handleReinstallOS = async () => {
  if (!selectedOS) {
    alert('Please select an operating system');
    return;
  }

  // Double confirmation
  if (!confirm('Are you sure you want to reinstall the OS? This will delete all data on the server and cause downtime of 10-20 minutes.')) {
    return;
  }

  setReinstallLoading(true);
  setReinstallResult(null);

  try {
    const token = sessionStorage.getItem('token');
    console.log(`Reinstalling server ID: ${id} with OS: ${selectedOS}`);

    const selectedOSName = availableOSOptions.find(os => os.id === selectedOS)?.name || selectedOS;

    // First update the UI with "in progress" indicator
    setReinstallResult({
      error: 0,
      message: `Sending reinstall command for ${selectedOSName}...`,
      processing: true
    });

    const response = await fetch(`/api.php?f=vps_reinstall_os`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token,
        server_id: id,
        os_id: selectedOS,
        hostname: server.hostname // Preserve current hostname
      })
    });

    const data = await response.json();

    // Set final result
    setReinstallResult({
      error: data.error,
      message: data.message || (data.error === 0 ? 'Reinstallation started successfully' : 'Failed to reinstall OS'),
      details: data.details || null,
      processing: false
    });

    if (data.error === 0) {
      // Success
      console.log(`Server reinstall command successful:`, data);

      // Close modal after success with a delay
      setTimeout(() => {
        setShowModal(false);
        setReinstallResult(null);
        setReinstallLoading(false);
      }, 5000);

      // Refresh server data after a delay
      setTimeout(() => {
        fetchServerDetails();
      }, 8000);
    }
  } catch (error) {
    console.error(`Error reinstalling OS:`, error);

    // Set error result for display
    setReinstallResult({
      error: 1,
      message: `Failed to reinstall OS: ${error.message}`,
      details: error.stack,
      processing: false
    });
  } finally {
    setReinstallLoading(false);
  }
};

// Add useEffect to fetch OS options when component mounts
useEffect(() => {
  if (server) {
    fetchAvailableOSOptions();
  }
}, [server, fetchAvailableOSOptions]);

  // Function to open modal with custom content
  const openModal = (title, body, footer) => {
    // Reset power action state when opening a new modal
    setPowerActionLoading(false);
    setPowerActionResult(null);

    setModalTitle(title);
    setModalBody(body);
    setModalFooter(footer);
    setShowModal(true);
  };

  const handleVpsUpgradeSuccess = (data) => {
    console.log('VPS upgrade invoice generated successfully:', data);
    

    // Suggest to the user to check their invoices
    setTimeout(() => {
      const goToInvoices = window.confirm("Would you like to view your invoices to complete the payment?");
      if (goToInvoices) {
   
        window.location.href = `/billing/invoices/`;
      }
    }, 500);
    
    // Refresh server details to get updated information
    setTimeout(() => {
      fetchServerDetails();
    }, 2000);
  };
  
  // Function to handle server power actions
  const handlePowerAction = async (action) => {
    if (!id) {
      alert('Server ID is not available');
      return;
    }

    setPowerActionLoading(true);
    try {
      const token = sessionStorage.getItem('token');
      console.log(`Sending power action ${action} for server ID: ${id}`);

      const response = await fetch(`/api.php?f=vps_power_action`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token,
          server_id: id,
          action: action
        })
      });

      const data = await response.json();

      // Set the action result for display in the modal
      setPowerActionResult(data);

      if (data.error === 0) {
        // Success
        console.log(`Server ${action} command successful:`, data);

        // Update power status immediately for better UI feedback
        if (action === 'start') {
          console.log(`Setting power status to 'online' after start action`);
          setPowerStatus('online');
        }
        else if (action === 'stop') {
          console.log(`Setting power status to 'offline' after stop action`);
          setPowerStatus('offline');
        }
        else if (action === 'restart') {
          console.log(`Setting power status to 'restarting' after restart action`);
          setPowerStatus('restarting');
        }

        // Schedule multiple status checks to confirm the status change
        const checkIntervals = [5000, 10000, 20000, 30000]; // 5s, 10s, 20s, 30s

        checkIntervals.forEach(interval => {
          setTimeout(() => {
            console.log(`Checking server status after ${interval/1000}s`);
            checkServerPowerStatus();
          }, interval);
        });

        // For restart action, set a final check to ensure we're back online
        if (action === 'restart') {
          setTimeout(() => {
            console.log(`Final check after restart - ensuring we're online`);
            if (powerStatus === 'restarting') {
              console.log(`Setting power status to 'online' after restart timeout`);
              setPowerStatus('online');
            }
          }, 35000);
        }

        // Close modal after success with a short delay
        setTimeout(() => {
          setShowModal(false);
          setPowerActionResult(null);
          setPowerActionLoading(false);
        }, 2000);

        // Refresh server data after a short delay
        setTimeout(() => {
          fetchServerDetails();
        }, 5000);
      }
    } catch (error) {
      console.error(`Error sending ${action} command:`, error);

      // Set error result for display
      setPowerActionResult({
        error: 1,
        message: `Failed to ${action} server: ${error.message}`,
        details: error.stack
      });
    } finally {
      setPowerActionLoading(false);
    }
  };

  // Function to check server power status
  const checkServerPowerStatus = async () => {
    // Use the ID from URL params directly, don't depend on server object being loaded
    if (!id) return;

    setPowerLoading(true);
    try {
      const token = sessionStorage.getItem('token');

      // Add a timestamp to prevent caching
      const timestamp = new Date().getTime();
      console.log(`Checking server power status for ID: ${id}`);

      // Use the server details endpoint since it contains the status
      const response = await fetch(`/api.php?f=vps_server_details&id=${id}&_=${timestamp}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        },
        body: JSON.stringify({ token })
      });

      const data = await response.json();
      console.log('Server status API response:', data);

      if (data.error === 0 && data.server && data.server.status) {
        // Map server status to power status
        const status = (data.server.status || '').toLowerCase();
        let powerState = 'unknown';

        // Map various status values to our standardized states
        if (status.includes('online') || status.includes('active') || status === 'running' || status === 'on' || status === 'started') {
          powerState = 'online';
        } else if (status.includes('offline') || status === 'stopped' || status === 'off') {
          powerState = 'offline';
        } else if (status.includes('restart') || status.includes('reboot')) {
          powerState = 'restarting';
        }

        console.log(`Mapping status "${status}" to power state "${powerState}"`)

        setPowerStatus(powerState);
        console.log(`Server status from API check: "${status}", mapped to power status: "${powerState}"`);

        // Update the server object if it's different
        if (server && JSON.stringify(server) !== JSON.stringify(data.server)) {
          setServer(data.server);
        }
      } else {
        console.error('Error checking power status:', data);
        setPowerStatus('unknown');
      }
    } catch (error) {
      console.error('Error checking server power status:', error);
      setPowerStatus('unknown');
    } finally {
      setPowerLoading(false);
    }
  };

  // Function to fetch server details
  const fetchServerDetails = useCallback(async () => {
    setLoading(true);
    try {
      const token = sessionStorage.getItem('token');
      const response = await fetch(`/api.php?f=vps_server_details&id=${id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      const data = await response.json();
      if (data.error === 0 && data.server) {
        console.log('Server data from API:', data.server);
        console.log('Location data:', {
          location: data.server.location,
          location_code: data.server.location_code,
          location_flag: data.server.location?.flag
        });
        
        // Log billing-related data for debugging
        console.log('Billing data from API:', {
          payment_period: data.server.payment_period,
          auto_renewal: data.server.auto_renewal,
          use_credit: data.server.use_credit,
          requirement_price: data.server.requirement_price,
          price: data.server.price,
          monthly_price: data.server.monthly_price
        });
        
        // Ensure payment_period is parsed as an integer if it exists
        if (data.server.payment_period) {
          data.server.payment_period = parseInt(data.server.payment_period);
        } else {
          // Default to monthly (1) if not provided
          data.server.payment_period = 1;
        }
        
        // Ensure auto_renewal is a boolean
        data.server.auto_renewal = data.server.auto_renewal === 1 || 
                                  data.server.auto_renewal === '1' || 
                                  data.server.auto_renewal === true;
        
        // Ensure use_credit is a boolean
        data.server.use_credit = data.server.use_credit === 1 || 
                                data.server.use_credit === '1' || 
                                data.server.use_credit === true;
        
        setServer(data.server);

        // Map server status to power status
        const status = (data.server.status || '').toLowerCase();
        let powerState = 'unknown';

        // Map various status values to our standardized states
        if (status.includes('online') || status.includes('active') || status === 'running' || status === 'on' || status === 'started') {
          powerState = 'online';
        } else if (status.includes('offline') || status === 'stopped' || status === 'off') {
          powerState = 'offline';
        } else if (status.includes('restart') || status.includes('reboot')) {
          powerState = 'restarting';
        }

        console.log(`Mapping status "${status}" to power state "${powerState}"`)

        setPowerStatus(powerState);
        console.log(`Server status from API: "${status}", mapped to power status: "${powerState}"`);

        // Set usage data from API response
        if (data.usage) {
          setUsageData(data.usage);
        }
      } else {
        console.error("Error fetching VPS server details:", data.message || "Unknown error");
      }
    } catch (error) {
      console.error("Error fetching VPS server details:", error);
    } finally {
      setLoading(false);
    }
  }, [id]);

  // Fetch traffic data
  const fetchTrafficData = useCallback(async (period = '24h') => {
    if (!server || !server.id) return;

    setLoadingTraffic(true);
    try {
      const token = sessionStorage.getItem('token');
      console.log(`Fetching traffic data for server ID: ${server.id}, period: ${period}`);

      const response = await fetch(`/api.php?f=vps_traffic_data&id=${server.id}&period=${period}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify({ token })
      });

      const data = await response.json();
      console.log('Traffic data response:', data);

      if (data.error === 0 && data.traffic) {
        console.log(`Received ${data.traffic.length} traffic data points`);
        setUsageData(data.traffic);
        calculateNetworkStats(data.traffic);
      } else {
        console.error('Error fetching traffic data:', data.message || 'Unknown error');
        // If we get an error, set empty data to avoid displaying stale data
        setUsageData([]);
        setNetworkStats({
          peakTraffic: 0,
          avgTraffic: 0,
          monthlyUsage: 0,
          packetsPerSec: 0
        });
      }
    } catch (error) {
      console.error('Error fetching traffic data:', error);
      // If we get an error, set empty data to avoid displaying stale data
      setUsageData([]);
      setNetworkStats({
        peakTraffic: 0,
        avgTraffic: 0,
        monthlyUsage: 0,
        packetsPerSec: 0
      });
    } finally {
      setLoadingTraffic(false);
    }
  }, [server]);

  // Calculate network statistics
  const calculateNetworkStats = (data) => {
    if (!data || data.length === 0) {
      setNetworkStats({
        peakTraffic: 0,
        avgTraffic: 0,
        monthlyUsage: 0,
        packetsPerSec: 0
      });
      return;
    }

    // Calculate peak traffic (highest network value)
    let peakTraffic = 0;
    let totalTraffic = 0;
    let totalPackets = 0;

    data.forEach(point => {
      if (point.network > peakTraffic) {
        peakTraffic = point.network;
      }
      totalTraffic += point.network;

      // Calculate packets if available
      if (point.read_kb && point.write_kb) {
        // Estimate packets based on KB/s (rough approximation)
        const estimatedPackets = (point.read_kb + point.write_kb) / 1.5; // Assuming average packet size
        totalPackets += estimatedPackets;
      }
    });

    // Average traffic in Mbps
    const avgTraffic = parseFloat((totalTraffic / data.length).toFixed(1));

    // Monthly usage in GB (approximate)
    const monthlyUsage = parseFloat((avgTraffic * 60*60*24*30 / 8 / 1024).toFixed(1));

    // Average packets per second
    let avgPacketsPerSec = 0;
    if (totalPackets > 0) {
      avgPacketsPerSec = Math.round(totalPackets / data.length);
    } else {
      // Fallback calculation if packet data not available
      avgPacketsPerSec = Math.round(avgTraffic * 100); // Example scaling factor
    }

    setNetworkStats({
      peakTraffic: parseFloat(peakTraffic.toFixed(1)),
      avgTraffic,
      monthlyUsage,
      packetsPerSec: avgPacketsPerSec
    });
  };

  // Function to toggle VNC modal
  const toggleVncModal = () => {
    setShowVncModal(!showVncModal);
  };

  // Function to handle VNC console connection
  const handleVncConnection = async () => {
    if (!id) {
      alert('Server ID is not available');
      return;
    }

    setLoadingVnc(true);
    try {
      const token = sessionStorage.getItem('token');
      console.log(`Fetching VNC details for server ID: ${id}`);

      const response = await fetch(`/api.php?f=vps_vnc_details&id=${id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      const data = await response.json();

      if (data.error === 0) {
        console.log('VNC details retrieved successfully:', data);

        // Store VNC details and open modal
        setVncDetails(data.vnc_details || {
          fallback_url: `https://virt.zetservers.com/console.php?vserverid=${id}`
        });
        setShowVncModal(true);

        // Log activity
        console.log(`VNC console opened for server ID: ${id}`);
      } else {
        console.error('Error fetching VNC details:', data.message || 'Unknown error');

        // Still open the modal but with fallback URL
        setVncDetails({
          fallback_url: `https://virt.zetservers.com/console.php?vserverid=${id}`
        });
        setShowVncModal(true);
      }
    } catch (error) {
      console.error('Error connecting to VNC console:', error);

      // Still open the modal but with fallback URL
      setVncDetails({
        fallback_url: `https://virt.zetservers.com/console.php?vserverid=${id}`
      });
      setShowVncModal(true);
    } finally {
      setLoadingVnc(false);
    }
  };

  // Handle period change from dropdown
  const handlePeriodChange = (e) => {
    const newPeriod = e.target.value;
    console.log(`Changing traffic period to: ${newPeriod}`);
    setTrafficPeriod(newPeriod);
    fetchTrafficData(newPeriod);
  };

  // Function to fetch reverse DNS records
// Function to fetch reverse DNS records
// Function to fetch reverse DNS records
const fetchRdnsRecords = useCallback(async (ip) => {
  setRdnsLoading(true);
  try {
    const token = sessionStorage.getItem('token');
    console.log(`Fetching reverse DNS for IP: ${ip}`);

    // First we need to get server details to find the IP ID
    const serverResponse = await fetch(`/api.php?f=vps_server_details&id=${id}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      },
      body: JSON.stringify({ token })
    });

    const serverData = await serverResponse.json();
    let ipId = null;

    // Find the IP ID for the given IP address
    if (serverData.error === 0 && serverData.server && serverData.server.ip_addresses) {
      // SolusVM may return IP addresses in different formats, check both structures
      if (serverData.server.ip_addresses.ipv4) {
        // Format: { ipv4: [ { ip, gateway, netmask, id }, ... ] }
        const ipObj = serverData.server.ip_addresses.ipv4.find(addr => addr.ip === ip);
        if (ipObj && ipObj.id) {
          ipId = ipObj.id;
          console.log(`Found IP ID ${ipId} for IP ${ip}`);
        }
      } else if (Array.isArray(serverData.server.ip_addresses)) {
        // Format: [ { address, type, id }, ... ]
        const ipObj = serverData.server.ip_addresses.find(addr => 
          addr.address === ip || addr.ip === ip
        );
        if (ipObj && ipObj.id) {
          ipId = ipObj.id;
          console.log(`Found IP ID ${ipId} for IP ${ip}`);
        }
      }
    }

    // If we found the IP ID, use it to fetch RDNS records
    if (ipId) {
      const response = await fetch(`/api.php?f=get_reverse_dns`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify({
          token,
          ip_id: ipId,
          domain: 'placeholder.domain.com' // Required by SolusVM API
        })
      });

      const data = await response.json();

      if (data.error === 0 && data.data) {
        console.log('Reverse DNS records:', data.data);
        setRdnsRecords(data.data);
        
        // Store the IP ID for later use
        if (data.data.length > 0 && data.data[0].id) {
          setSelectedIpId(data.data[0].id);
        } else {
          setSelectedIpId(ipId); // Store the IP ID we found earlier
        }
        
        return data.data;
      }
    } else {
      console.log(`Could not find IP ID for ${ip}, trying direct IP lookup`);
      
      // Fallback to direct IP lookup if we couldn't find the IP ID
      const response = await fetch(`/api.php?f=get_reverse_dns`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify({
          token,
          ip: ip,
          domain: 'placeholder.domain.com' // Required by SolusVM API
        })
      });

      const data = await response.json();

      if (data.error === 0 && data.data) {
        console.log('Reverse DNS records:', data.data);
        setRdnsRecords(data.data);
        
        // Store the IP ID if available
        if (data.data.length > 0 && data.data[0].id) {
          setSelectedIpId(data.data[0].id);
        }
        
        return data.data;
      }
    }
    
    console.error('Error fetching reverse DNS records: No data returned');
    setRdnsRecords([]);
    return [];
  } catch (error) {
    console.error('Error fetching reverse DNS records:', error);
    setRdnsRecords([]);
    return [];
  } finally {
    setRdnsLoading(false);
  }
}, [id]);

// Function to create or update reverse DNS
const handleRdnsUpdate = async (e) => {
  e.preventDefault();

  if (!rdnsDomain) {
    alert('Please enter a valid domain name');
    return;
  }

  setRdnsUpdateLoading(true);
  setRdnsUpdateResult(null);

  try {
    const token = sessionStorage.getItem('token');
    console.log(`Updating reverse DNS for IP: ${selectedIp} to domain: ${rdnsDomain}`);

    // Use the direct RDNS update endpoint which doesn't require an IP ID
    const response = await fetch(`/api.php?f=direct_rdns_update`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token,
        ip: selectedIp,
        domain: rdnsDomain
      })
    });

    const data = await response.json();
    console.log("Direct RDNS update response:", data);
    setRdnsUpdateResult(data);

    if (data.error === 0) {
      console.log("Reverse DNS update successful!");

      // Close modal after success with a delay
      setTimeout(() => {
        setShowModal(false);
        setRdnsUpdateResult(null);
        setRdnsUpdateLoading(false);

        // Refresh server details to get updated DNS info
        fetchServerDetails();
      }, 2000);
    } else {
      console.error("Reverse DNS update failed:", data.message);
    }
  } catch (error) {
    console.error(`Error updating reverse DNS:`, error);
    setRdnsUpdateResult({
      error: 1,
      message: `Failed to update reverse DNS: ${error.message}`,
      details: error.stack
    });
  } finally {
    setRdnsUpdateLoading(false);
  }
};

// Function to directly update reverse DNS without needing IP ID
const handleDirectRdnsUpdate = async (e) => {
  e.preventDefault();

  if (!rdnsDomain) {
    alert('Please enter a valid domain name');
    return;
  }

  setRdnsUpdateLoading(true);
  setRdnsUpdateResult(null);

  try {
    const token = sessionStorage.getItem('token');
    console.log(`Directly updating reverse DNS for IP: ${selectedIp} to domain: ${rdnsDomain}`);
    
    const response = await fetch(`/api.php?f=direct_rdns_update`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token,
        ip: selectedIp,
        domain: rdnsDomain
      })
    });

    const data = await response.json();
    console.log(`Direct RDNS update response:`, data);
    setRdnsUpdateResult(data);

    if (data.error === 0) {
      console.log(`Direct reverse DNS update successful!`);

      // Close modal after success with a delay
      setTimeout(() => {
        setShowModal(false);
        setRdnsUpdateResult(null);
        setRdnsUpdateLoading(false);

        // Refresh server details to get updated DNS info
        fetchServerDetails();
      }, 2000);
    } else {
      console.error(`Direct RDNS update failed:`, data.message || "Unknown error");
    }
  } catch (error) {
    console.error(`Error in direct RDNS update:`, error);
    setRdnsUpdateResult({
      error: 1,
      message: `Failed to update reverse DNS: ${error.message}`,
      details: error.stack
    });
  } finally {
    setRdnsUpdateLoading(false);
  }
};

  // Function to delete reverse DNS
// Function to delete reverse DNS
const handleRdnsDelete = async (id) => {
  if (!confirm('Are you sure you want to delete this reverse DNS record?')) {
    return;
  }

  setRdnsUpdateLoading(true);

  try {
    const token = sessionStorage.getItem('token');
    console.log(`Deleting reverse DNS for IP: ${selectedIp}`);

    // Use the direct endpoint for deletion too
    const response = await fetch(`/api.php?f=direct_rdns_delete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token,
        ip: selectedIp
      })
    });

    const data = await response.json();
    console.log("RDNS delete response:", data);

    if (data.error === 0) {
      console.log("Reverse DNS deletion successful");
      alert('Reverse DNS record deleted successfully');

      // Refresh server details to get updated DNS info
      fetchServerDetails();
    } else {
      console.error("Error deleting reverse DNS:", data.message || 'Unknown error');
      alert(`Failed to delete reverse DNS: ${data.message || 'Unknown error'}`);
    }
  } catch (error) {
    console.error('Error deleting reverse DNS:', error);
    alert(`Failed to delete reverse DNS: ${error.message}`);
  } finally {
    setRdnsUpdateLoading(false);
  }
};

  // Function to open the reverse DNS modal
// Function to open the reverse DNS modal
const openRdnsModal = (ip, currentDomain = '', ipId = null) => {
  setSelectedIp(ip);
  setSelectedIpId(ipId);
  setRdnsDomain(currentDomain);
  setRdnsUpdateResult(null);

  const title = ipId ? 'Update Reverse DNS' : 'Set Reverse DNS';
  const body = (
    <div>
      <div className="alert alert-info mb-3">
        <i className="fa fa-info-circle me-2"></i>
        Reverse DNS (rDNS) allows you to specify the domain name that will be returned when a reverse lookup is performed on your IP address.
      </div>

      <form onSubmit={handleRdnsUpdate}>
        <div className="form-group mb-3">
          <label className="form-label">IP Address</label>
          <input
            type="text"
            className="form-control"
            value={ip}
            disabled
          />
          {selectedIpId && (
            <small className="form-text text-muted">
              IP ID: {selectedIpId}
            </small>
          )}
        </div>

        <div className="form-group mb-3">
          <label className="form-label">Domain Name</label>
          <input
            type="text"
            className="form-control"
            value={rdnsDomain}
            onChange={(e) => setRdnsDomain(e.target.value)}
            placeholder="e.g., server.yourdomain.com"
            required
          />
          <small className="form-text text-muted">
            Enter a fully qualified domain name that points to this IP address.
          </small>
        </div>

        {rdnsUpdateLoading && (
          <div className="alert alert-info mt-3">
            <div className="d-flex align-items-center">
              <div className="spinner-border spinner-border-sm me-2" role="status"></div>
              Processing reverse DNS update...
            </div>
          </div>
        )}

        {rdnsUpdateResult && (
          <div className={`alert ${rdnsUpdateResult.error === 0 ? 'alert-success' : 'alert-danger'} mt-3`}>
            <i className={`fa ${rdnsUpdateResult.error === 0 ? 'fa-check-circle' : 'fa-exclamation-circle'} me-2`}></i>
            {rdnsUpdateResult.message}
            {rdnsUpdateResult.details && (
              <div className="mt-2 small">
                <strong>Details:</strong><br />
                <pre className="mb-0">{rdnsUpdateResult.details}</pre>
              </div>
            )}
          </div>
        )}
      </form>
    </div>
  );

  const footer = (
    <div>
      <button className="btn btn-secondary" onClick={toggleModal}>Cancel</button>
      <button
        className="btn btn-primary ms-2"
        onClick={handleRdnsUpdate}
        disabled={rdnsUpdateLoading}
      >
        {rdnsUpdateLoading ? (
          <>
            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
            Updating...
          </>
        ) : (ipId ? 'Update' : 'Create')}
      </button>
    </div>
  );

  openModal(title, body, footer);
};

  // Fetch server details on component mount
  useEffect(() => {
    fetchServerDetails();
  }, [fetchServerDetails]);

  // Initialize newHostname when server data is loaded
  useEffect(() => {
    if (server && (server.hostname || server.name)) {
      setNewHostname(server.hostname || server.name);
    }
  }, [server]);

  // Fetch reverse DNS records when server data is loaded
  useEffect(() => {
    if (server && server.main_ip) {
      fetchRdnsRecords(server.main_ip);
    }
  }, [server, fetchRdnsRecords]);

  // Check power status when component mounts and when server data is loaded
  useEffect(() => {
    // Check power status immediately
    checkServerPowerStatus();

    // Set up interval to check power status every 30 seconds
    const intervalId = setInterval(checkServerPowerStatus, 30000);

    // Clean up interval on component unmount
    return () => clearInterval(intervalId);
  }, [id]); // Only re-run if the ID changes

  // Load traffic data when server changes or tab changes
  useEffect(() => {
    if (server && server.id && activeTab === 'network') {
      console.log(`Network tab activated, fetching traffic data with period: ${trafficPeriod}`);
      fetchTrafficData(trafficPeriod);
    }
  }, [server, activeTab, fetchTrafficData, trafficPeriod]);

  // Toggle password visibility
  const togglePassword = () => {
    setShowPassword(!showPassword);
  };

  // Copy text to clipboard
  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    alert('Copied to clipboard!');
  };

  // Function to get billing cycle name
  const getBillingCycleName = (cycle) => {
    // Handle both string and numeric values
    const cycleValue = String(cycle).toLowerCase();
    
    switch (cycleValue) {
      case '3':
      case 'quarterly':
        return 'Quarterly';
      case '6':
      case 'semi-annual':
        return 'Semi-Annual';
      case '12':
      case 'annual':
        return 'Annual';
      case '1':
      default:
        return 'Monthly';
    }
  };

  // Modal content for power actions
  const startModal = {
    title: 'Start Server',
    body: (
      <div>
        <div className="alert alert-warning mb-3">
          <i className="fa fa-info-circle me-2"></i>
          This action may take up to 2 minutes. If the server is not online within this time frame, please check the console for further details.
        </div>

        {powerActionLoading && (
          <div className="alert alert-info mt-3">
            <div className="d-flex align-items-center">
              <div className="spinner-border spinner-border-sm me-2" role="status"></div>
              Sending power on command to server...
            </div>
          </div>
        )}

        {powerActionResult && (
          <div className={`alert ${powerActionResult.error === 0 ? 'alert-success' : 'alert-danger'} mt-3`}>
            <i className={`fa ${powerActionResult.error === 0 ? 'fa-check-circle' : 'fa-exclamation-circle'} me-2`}></i>
            {powerActionResult.message}
            {powerActionResult.details && (
              <div className="mt-2 small">
                <strong>Details:</strong><br />
                <pre className="mb-0">{powerActionResult.details}</pre>
              </div>
            )}
          </div>
        )}
      </div>
    ),
    footer: (
      <div>
        <button className="btn btn-secondary" onClick={toggleModal}>Cancel</button>
        <button
          className="btn btn-success ms-2"
          onClick={() => handlePowerAction('start')}
          disabled={powerActionLoading || powerStatus === 'online'}
        >
          {powerActionLoading ? (
            <>
              <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Starting...
            </>
          ) : 'Start Server'}
        </button>
      </div>
    )
  };

  const stopModal = {
    title: 'Stop Server',
    body: (
      <div>
        <div className="alert alert-warning mb-3">
          <i className="fa fa-info-circle me-2"></i>
          This action will shut down your server, and you will need to manually restart it afterward.
        </div>
        <div className="alert alert-danger mt-3 mb-0">
          <i className="fa fa-exclamation-triangle me-2"></i>
          <strong>WARNING:</strong> This will perform a hard shutdown, which may cause data loss if applications are running.
        </div>

        {powerActionLoading && (
          <div className="alert alert-info mt-3">
            <div className="d-flex align-items-center">
              <div className="spinner-border spinner-border-sm me-2" role="status"></div>
              Sending shutdown command to server...
            </div>
          </div>
        )}

        {powerActionResult && (
          <div className={`alert ${powerActionResult.error === 0 ? 'alert-success' : 'alert-danger'} mt-3`}>
            <i className={`fa ${powerActionResult.error === 0 ? 'fa-check-circle' : 'fa-exclamation-circle'} me-2`}></i>
            {powerActionResult.message}
            {powerActionResult.details && (
              <div className="mt-2 small">
                <strong>Details:</strong><br />
                <pre className="mb-0">{powerActionResult.details}</pre>
              </div>
            )}
          </div>
        )}
      </div>
    ),
    footer: (
      <div>
        <button className="btn btn-secondary" onClick={toggleModal}>Cancel</button>
        <button
          className="btn btn-danger ms-2"
          onClick={() => handlePowerAction('stop')}
          disabled={powerActionLoading || powerStatus === 'offline'}
        >
          {powerActionLoading ? (
            <>
              <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Stopping...
            </>
          ) : 'Stop Server'}
        </button>
      </div>
    )
  };

  const rebootModal = {
    title: 'Reboot Server',
    body: (
      <div>
        <div className="alert alert-warning mb-3">
          <i className="fa fa-info-circle me-2"></i>
          After rebooting, please allow up to 2 minutes for the server to become accessible again.
        </div>
        <div className="alert alert-danger mt-3 mb-0">
          <i className="fa fa-exclamation-triangle me-2"></i>
          <strong>WARNING:</strong> This will perform a hard reboot, which may cause data loss if applications are running.
        </div>

        {powerActionLoading && (
          <div className="alert alert-info mt-3">
            <div className="d-flex align-items-center">
              <div className="spinner-border spinner-border-sm me-2" role="status"></div>
              Sending reboot command to server...
            </div>
          </div>
        )}

        {powerActionResult && (
          <div className={`alert ${powerActionResult.error === 0 ? 'alert-success' : 'alert-danger'} mt-3`}>
            <i className={`fa ${powerActionResult.error === 0 ? 'fa-check-circle' : 'fa-exclamation-circle'} me-2`}></i>
            {powerActionResult.message}
            {powerActionResult.details && (
              <div className="mt-2 small">
                <strong>Details:</strong><br />
                <pre className="mb-0">{powerActionResult.details}</pre>
              </div>
            )}
          </div>
        )}
      </div>
    ),
    footer: (
      <div>
        <button className="btn btn-secondary" onClick={toggleModal}>Cancel</button>
        <button
          className="btn btn-warning ms-2"
          onClick={() => handlePowerAction('restart')}
          disabled={powerActionLoading || powerStatus === 'offline'}
        >
          {powerActionLoading ? (
            <>
              <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Rebooting...
            </>
          ) : 'Reboot Server'}
        </button>
      </div>
    )
  };

  // Add the handler function
const handleTerminate = async () => {

  setTerminateLoading(true);
  setTerminateResult(null);

  try {
    const token = sessionStorage.getItem('token');
    console.log(`Sending terminate request for server ID: ${id}`);

    const response = await fetch(`/api.php?f=vps_terminate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token,
        server_id: id
      })
    });

    const data = await response.json();
    setTerminateResult(data);

    if (data.error === 0) {
      console.log(`Server termination successful:`, data);

      setTimeout(() => {
        setShowModal(false);
        setTerminateResult(null);
        setTerminateLoading(false);

        // Redirect to services page
        navigate('/services');
      }, 3000);
    }
  } catch (error) {
    console.error(`Error terminating server:`, error);
    setTerminateResult({
      error: 1,
      message: `Failed to terminate server: ${error.message}`,
      details: error.stack
    });
  } finally {
    setTerminateLoading(false);
  }
};

// Handler function for resetting the password
const handleResetPassword = async () => {
  setResetPasswordLoading(true);
  setResetPasswordResult(null);

  try {
    const token = sessionStorage.getItem('token');
    console.log(`Sending password reset request for server ID: ${id}`);

    const response = await fetch(`/api.php?f=vps_reset_password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token,
        server_id: id
      })
    });

    const data = await response.json();
    setResetPasswordResult(data);

    if (data.error === 0) {
      console.log(`Password reset successful:`, data);

      // Update the server object with the new password if provided in the response
      if (data.new_password) {
        setServer({
          ...server,
          password: data.new_password
        });
      }

      // Close modal after success with a delay
      setTimeout(() => {
        setShowModal(false);
        setResetPasswordResult(null);
        setResetPasswordLoading(false);

        // Refresh server details to get the updated password
        fetchServerDetails();
      }, 3000);
    }
  } catch (error) {
    console.error(`Error resetting password:`, error);
    setResetPasswordResult({
      error: 1,
      message: `Failed to reset password: ${error.message}`,
      details: error.stack
    });
  } finally {
    setResetPasswordLoading(false);
  }
};

// Handler function for updating the hostname
const handleUpdateHostname = async (e) => {
  e.preventDefault();

  // Validate hostname
  if (!newHostname || newHostname.trim() === '') {
    alert('Please enter a valid hostname');
    return;
  }

  setHostnameUpdateLoading(true);
  setHostnameUpdateResult(null);

  try {
    const token = sessionStorage.getItem('token');
    console.log(`Updating hostname for server ID: ${id} to: ${newHostname}`);

    // Call the API to update the hostname
    const response = await fetch(`/api.php?f=vps_update_hostname`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token,
        server_id: id,
        hostname: newHostname.trim()
      })
    });

    const data = await response.json();
    setHostnameUpdateResult(data);

    if (data.error === 0) {
      console.log(`Hostname update successful:`, data);

      // Update the server object with the new hostname
      setServer({
        ...server,
        hostname: newHostname.trim()
      });

      // Show success message
      alert('Hostname updated successfully');

      // Refresh server details to confirm the update
      setTimeout(() => {
        fetchServerDetails();
      }, 1000);
    } else {
      // Show error message
      alert(`Failed to update hostname: ${data.message || 'Unknown error'}`);
    }
  } catch (error) {
    console.error(`Error updating hostname:`, error);
    setHostnameUpdateResult({
      error: 1,
      message: `Failed to update hostname: ${error.message}`,
      details: error.stack
    });

    // Show error message
    alert(`Failed to update hostname: ${error.message}`);
  } finally {
    setHostnameUpdateLoading(false);
  }
};

// Define the terminate confirmation modal
const terminateModal = {
  title: 'Terminate Server',
  body: (
    <div>
      <div className="alert alert-danger mb-3">
        <i className="fa fa-exclamation-triangle me-2"></i>
        <strong>WARNING:</strong> This will permanently delete your server and all data on it. This action cannot be undone.
      </div>

      <p>Are you sure you want to terminate this server?</p>

      {terminateLoading && (
        <div className="alert alert-info mt-3">
          <div className="d-flex align-items-center">
            <div className="spinner-border spinner-border-sm me-2" role="status"></div>
            Processing termination request...
          </div>
        </div>
      )}

      {terminateResult && (
        <div className={`alert ${terminateResult.error === 0 ? 'alert-success' : 'alert-danger'} mt-3`}>
          <i className={`fa ${terminateResult.error === 0 ? 'fa-check-circle' : 'fa-exclamation-circle'} me-2`}></i>
          {terminateResult.message}
          {terminateResult.details && (
            <div className="mt-2 small">
              <strong>Details:</strong><br />
              <pre className="mb-0">{terminateResult.details}</pre>
            </div>
          )}
        </div>
      )}
    </div>
  ),
  footer: (
    <div>
      <button className="btn btn-secondary" onClick={toggleModal}>Cancel</button>
      <button
        className="btn btn-danger ms-2"
        onClick={handleTerminate}
        disabled={terminateLoading}
      >
        {terminateLoading ? (
          <>
            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
            Terminating...
          </>
        ) : 'Terminate'}
      </button>
    </div>
  )
};

// Define the reset password modal
const resetPasswordModal = {
  title: 'Reset Password',
  body: (
    <div>
      <div className="alert alert-warning mb-3">
        <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
        This will reset your server's root password to a new random password. You will need to use the new password to access your server.
      </div>

      <p>Are you sure you want to reset the password for this server?</p>

      {resetPasswordLoading && (
        <div className="alert alert-info mt-3">
          <div className="d-flex align-items-center">
            <div className="spinner-border spinner-border-sm me-2" role="status"></div>
            Processing password reset request...
          </div>
        </div>
      )}

      {resetPasswordResult && (
        <div className={`alert ${resetPasswordResult.error === 0 ? 'alert-success' : 'alert-danger'} mt-3`}>
          <i className={`fa ${resetPasswordResult.error === 0 ? 'fa-check-circle' : 'fa-exclamation-circle'} me-2`}></i>
          {resetPasswordResult.message}
          {resetPasswordResult.new_password && (
            <div className="mt-2">
              <strong>New Password:</strong> {resetPasswordResult.new_password}
            </div>
          )}
          {resetPasswordResult.details && (
            <div className="mt-2 small">
              <strong>Details:</strong><br />
              <pre className="mb-0">{resetPasswordResult.details}</pre>
            </div>
          )}
        </div>
      )}
    </div>
  ),
  footer: (
    <div>
      <button className="btn btn-secondary" onClick={toggleModal}>Cancel</button>
      <button
        className="btn btn-primary ms-2"
        onClick={handleResetPassword}
        disabled={resetPasswordLoading}
      >
        {resetPasswordLoading ? (
          <>
            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
            Resetting...
          </>
        ) : 'Reset Password'}
      </button>
    </div>
  )
};

// Update the reinstallModal object
const reinstallModal = {
  title: 'Reinstall Operating System',
  body: (
    <div className="form-group">
      <label className="form-label mb-2">Select Operating System:</label>

      {availableOSOptions.length === 0 ? (
        <div className="text-center py-3">
          <div className="spinner-border text-primary" role="status">
           
          </div>
          <p className="mt-2">Loading operating systems...</p>
        </div>
      ) : (
        <select
          name="os"
          className="form-control custom-select mb-3"
          value={selectedOS}
          onChange={(e) => setSelectedOS(e.target.value)}
          disabled={reinstallLoading}
        >
          {/* Group options by category */}
          {(() => {
            const categories = {};

            // Group options by category
            availableOSOptions.forEach(option => {
              const category = option.category || 'Other';
              if (!categories[category]) {
                categories[category] = [];
              }
              categories[category].push(option);
            });

            // Convert to JSX with optgroups
            return Object.entries(categories).map(([category, options]) => (
              <optgroup key={category} label={category}>
                {options.map(option => (
                  <option key={option.id} value={option.id}>
                    {option.name}
                  </option>
                ))}
              </optgroup>
            ));
          })()}
        </select>
      )}

      <div className="alert alert-info mb-3">
        <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
        After initiating the reinstallation procedure, your server will be rebooted and will be unavailable during the installation process (typically 10-20 minutes).
      </div>

      <div className="alert alert-danger mb-0">
        <i className="fa fa-exclamation-triangle me-2" style={{paddingRight: '0.5rem'}}></i>
        <strong>WARNING:</strong> This function will delete all existing data from your server. Make sure you have backed up any important data before proceeding.
      </div>

      {reinstallLoading && (
        <div className="alert alert-info mt-3">
          <div className="d-flex align-items-center">
            <div className="spinner-border spinner-border-sm me-2" role="status"></div>
            Sending reinstall command to server...
          </div>
        </div>
      )}

      {reinstallResult && (
        <div className={`alert ${reinstallResult.error === 0 ? 'alert-success' : 'alert-danger'} mt-3`}>
          <i className={`fa ${reinstallResult.error === 0 ? 'fa-check-circle' : 'fa-exclamation-circle'} me-2`}></i>
          {reinstallResult.message}

          {reinstallResult.processing && (
            <div className="progress mt-2" style={{height: "5px"}}>
              <div className="progress-bar progress-bar-striped progress-bar-animated"
                   style={{width: "100%"}} />
            </div>
          )}

          {reinstallResult.details && (
            <div className="mt-2 small">
              <strong>Details:</strong><br />
              <pre className="mb-0" style={{maxHeight: "150px", overflow: "auto"}}>
                {typeof reinstallResult.details === 'object'
                  ? JSON.stringify(reinstallResult.details, null, 2)
                  : reinstallResult.details}
              </pre>
            </div>
          )}

          {reinstallResult.error === 0 && !reinstallResult.processing && (
            <div className="mt-2">
              <p className="mb-0">
                The reinstall process is now running on the server. The system will be unavailable during installation.
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  ),
  footer: (
    <div>
      <button className="btn btn-secondary" onClick={toggleModal} disabled={reinstallLoading}>Cancel</button>
      <button
        className="btn btn-danger ms-2"
        onClick={handleReinstallOS}
        disabled={reinstallLoading || !selectedOS || availableOSOptions.length === 0}
      >
        {reinstallLoading ? (
          <>
            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
            Reinstalling...
          </>
        ) : 'Reinstall OS'}
      </button>
    </div>
  )
};

  // Loading state
  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: "300px" }}>
        <div className="spinner-border text-primary" role="status">
    
        </div>
      </div>
    );
  }

  // Error state
  if (!server) {
    return (
      <div className="alert alert-danger" role="alert">
        VPS server not found. <Link to="/services">Return to services</Link>
      </div>
    );
  }

  return (
    <>
      {/* VNC Modal */}
      <VncModal
        isOpen={showVncModal}
        toggle={toggleVncModal}
        vncDetails={vncDetails}
        serverId={id}
      />
        {/* VPS Upgrade Modal */}
  <VpsUpgradeModal
    isOpen={showVpsUpgradeModal}
    onClose={toggleVpsUpgradeModal}
    server={server}
    onSuccess={handleVpsUpgradeSuccess}
  />

      <div className="page-header">
        <div className="page-leftheader">
          <ol className="breadcrumb">
            <li className="breadcrumb-item"><Link to="/">Dashboard</Link></li>
            <li className="breadcrumb-item"><Link to="/services">Services</Link></li>
            <li className="breadcrumb-item"> {server.hostname || server.name}</li>
          </ol>

        </div>
        <div className="page-rightheader ml-auto">
          <div className="dropdown">
            <a href="#" className="nav-link pr-0 leading-none" data-toggle="dropdown">
              <button className="btn btn-success">New Service</button>
            </a>
            <div className="dropdown-menu">
              <Link className="dropdown-item d-flex" to="/vps/order">
                <div className="mt-1">VPS Server</div>
              </Link>
              <Link className="dropdown-item d-flex" to="/dedicatedorder">
                <div className="mt-1">Dedicated Server</div>
              </Link>
              <Link className="dropdown-item d-flex" to="/cloudorder">
                <div className="mt-1">Cloud Server</div>
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="row">
        <div className="col-12">
          <DiscountAlert />

          <div className="card">
            <div className="card-header">
              <div className="card-title d-flex align-items-center">
                <span className="me-2">{server.hostname || server.name}</span>
                {powerStatus === 'online' && <span className="badge bg-success">Online</span>}
                {powerStatus === 'offline' && <span className="badge bg-danger">Offline</span>}
                {powerStatus === 'restarting' && <span className="badge bg-warning">Restarting</span>}
                {powerStatus === 'unknown' && <span className="badge bg-secondary">Unknown</span>}
              </div>
              <div className="card-options">
                <button className="btn btn-success btn-sm me-2" onClick={() => openModal(reinstallModal.title, reinstallModal.body, reinstallModal.footer)}>
                  <i className="fa fa-cogs me-1"></i> Reinstall
                </button>
                {powerStatus === 'offline' && (
                  <button
                    className="btn btn-success btn-sm me-2"
                    onClick={() => openModal(startModal.title, startModal.body, startModal.footer)}
                  >
                    <i className="fa fa-play me-1"></i> Start
                  </button>
                )}
                <button
                  className="btn btn-warning btn-sm me-2"
                  onClick={() => openModal(rebootModal.title, rebootModal.body, rebootModal.footer)}
                  disabled={powerStatus === 'offline'}
                >
                  <i className="fa fa-refresh me-1"></i> Reboot
                </button>
                <button
                  className="btn btn-danger btn-sm"
                  onClick={() => openModal(stopModal.title, stopModal.body, stopModal.footer)}
                  disabled={powerStatus === 'offline'}
                >
                  <i className="fa fa-power-off me-1"></i> Shutdown
                </button>
              </div>
            </div>
            <div className="card-body">
              <div className="panel panel-primary">
                <div className="tab-menu-heading">
                  <div className="tabs-menu">
                    <ul className="nav panel-tabs">
                      <li>
                        <a
                          className={activeTab === 'overview' ? 'active' : ''}
                          onClick={() => setActiveTab('overview')}
                          style={{cursor: 'pointer'}}
                        >
                          <i className="si si-screen-desktop me-1"></i> Overview
                        </a>
                      </li>
                      <li>
                        <a
                          className={activeTab === 'network' ? 'active' : ''}
                          onClick={() => setActiveTab('network')}
                          style={{cursor: 'pointer'}}
                        >
                          <i className="si si-graph me-1"></i> Network
                        </a>
                      </li>

                      <li>
                        <a
                          className={activeTab === 'settings' ? 'active' : ''}
                          onClick={() => setActiveTab('settings')}
                          style={{cursor: 'pointer'}}
                        >
                          <i className="si si-settings me-1"></i> Settings
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div className="panel-body tabs-menu-body">
                  <div className="tab-content">
                    {/* Overview Tab */}
                    {activeTab === 'overview' && (
                      <div className="tab-pane active">
                        <div className="row">
                          {/* Server Access Card */}
                          <div className="col-xl-4 col-lg-6 col-md-12 mb-4">
                            <div className="card shadow-sm h-100">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-server me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Server Access</h3>
                              </div>
                              <div className="card-body">
                                <h5 className="text-muted mb-3">Quick Actions</h5>
                                <div className="row">
                                  <div className="col-6 mb-3">
                                    <button
                                      className="btn btn-light btn-block rounded-3 w-100 h-100 py-3 shadow-sm"
                                      onClick={() => openModal(startModal.title, startModal.body, startModal.footer)}
                                      disabled={powerStatus === 'online' || powerStatus === 'restarting'}
                                    >
                                      <i className="fa fa-power-off fa-2x d-block mb-2 text-success"></i>
                                      Turn On
                                    </button>
                                  </div>
                                  <div className="col-6 mb-3">
                                    <button
                                      className="btn btn-light btn-block rounded-3 w-100 h-100 py-3 shadow-sm"
                                      onClick={() => openModal(stopModal.title, stopModal.body, stopModal.footer)}
                                      disabled={powerStatus === 'offline' || powerStatus === 'restarting'}
                                    >
                                      <i className="fa fa-power-off fa-2x d-block mb-2 text-danger"></i>
                                      Turn Off
                                    </button>
                                  </div>
                                  <div className="col-6 mb-3">
                                    <button
                                      className="btn btn-light btn-block rounded-3 w-100 h-100 py-3 shadow-sm"
                                      onClick={() => openModal(rebootModal.title, rebootModal.body, rebootModal.footer)}
                                      disabled={powerStatus === 'offline' || powerStatus === 'restarting'}
                                    >
                                      <i className="fa fa-refresh fa-2x d-block mb-2 text-warning"></i>
                                      Reboot
                                    </button>
                                  </div>
                                  <div className="col-6 mb-3">
                                    <button
                                      onClick={handleVncConnection}
                                      className="btn btn-light btn-block rounded-3 w-100 h-100 py-3 shadow-sm"
                                      disabled={loadingVnc}
                                    >
                                      {loadingVnc ? (
                                        <>
                                          <div className="spinner-border spinner-border-sm mb-2" role="status"></div>
                                          <span className="d-block">Connecting...</span>
                                        </>
                                      ) : (
                                        <>
                                          <i className="fa fa-desktop fa-2x d-block mb-2 text-primary"></i>
                                          Console
                                        </>
                                      )}
                                    </button>
                                  </div>

                                </div>

                                <h5 className="text-muted mb-3">Access Details</h5>
                                <ul className="list-group list-group-flush">
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-globe text-primary me-2"></i> IP Address:</span>
                                    <div className="d-flex align-items-center">
                                      <span className="me-2" style={{paddingRight: '0.5rem'}}>{server.main_ip}</span>
                                      <button
                                        className="btn btn-sm btn-light"
                                        onClick={() => copyToClipboard(server.main_ip)}
                                        title="Copy to clipboard"
                                      >
                                        <i className="fa fa-copy"></i>
                                      </button>
                                    </div>
                                  </li>
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-user text-primary me-2"></i> Username:</span>
                                    <div className="d-flex align-items-center">
                                      <span className="me-2" style={{paddingRight: '0.5rem'}}>{server.username || 'root'}</span>
                                      <button
                                        className="btn btn-sm btn-light"
                                        onClick={() => copyToClipboard(server.username || 'root')}
                                        title="Copy to clipboard"
                                      >
                                        <i className="fa fa-copy"></i>
                                      </button>
                                    </div>
                                  </li>
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-key text-primary me-2"></i> Password:</span>
                                    <div className="d-flex align-items-center">
                                      <span className="me-2" style={{paddingRight: '0.5rem'}}>
                                        {showPassword ? server.password : '••••••••••••'}
                                      </span>
                                      <button
                                        className="btn btn-sm btn-light me-2"
                                        onClick={togglePassword}
                                        title={showPassword ? "Hide password" : "Show password"}
                                      >
                                        <i className={`fa fa-${showPassword ? 'eye-slash' : 'eye'}`}></i>
                                      </button>
                                      <button
                                        className="btn btn-sm btn-light me-2"
                                        onClick={() => copyToClipboard(server.password)}
                                        title="Copy to clipboard"
                                      >
                                        <i className="fa fa-copy"></i>
                                      </button>
                                      <button
                                        className="btn btn-sm btn-warning"
                                        onClick={() => openModal(resetPasswordModal.title, resetPasswordModal.body, resetPasswordModal.footer)}
                                        title="Reset password"
                                      >
                                        <i className="fa fa-refresh"></i>
                                      </button>
                                    </div>
                                  </li>
                                </ul>
                              </div>

<button 
    className="btn btn-primary w-100"
    onClick={toggleVpsUpgradeModal}
  >
    <i className="fa fa-arrow-up me-1"></i> Upgrade Plan
  </button>
                            </div>
                          </div>

                          {/* Status and Hardware Card */}
                          <div className="col-xl-4 col-lg-6 col-md-12 mb-4">
                            <div className="card shadow-sm h-100">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-info-circle me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Status & Hardware</h3>
                              </div>
                              <div className="card-body">
                                <h5 className="text-muted mb-3">Server Status</h5>
                                <ul className="list-group list-group-flush mb-4">
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-plug text-primary me-2"></i> Power Status:</span>
                                    {powerLoading ? (
                                      <div className="d-flex align-items-center">
                                        <div className="spinner-border spinner-border-sm me-2" role="status" style={{ width: '1rem', height: '1rem' }}></div>
                                        <span>Checking...</span>
                                      </div>
                                    ) : (
                                      <>
                                        {powerStatus === 'online' && <span className="badge bg-success">Online</span>}
                                        {powerStatus === 'offline' && <span className="badge bg-danger">Offline</span>}
                                        {powerStatus === 'restarting' && <span className="badge bg-warning">Restarting</span>}
                                        {powerStatus === 'unknown' && <span className="badge bg-secondary">Unknown</span>}
                                      </>
                                    )}
                                  </li>
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-server text-primary me-2"></i> Hostname:</span>
                                    <span className="text-muted">{server.hostname || server.name}</span>
                                  </li>
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-building text-primary me-2"></i> Location:</span>
                                    <span className="text-muted">
                                      {server.location_flag && (
                                        <i className={`flag flag-${server.location_flag}`} style={{marginRight: '8px'}}></i>
                                      )}
                                      {server.location || 'Unknown'}
                                    </span>
                                  </li>
                                </ul>

                                <h5 className="text-muted mb-3">Hardware Specifications</h5>
                                <ul className="list-group list-group-flush mb-4">
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-microchip text-primary me-2"></i> CPU:</span>
                                    <span className="text-muted">{server.cpu || 'N/A'}</span>
                                  </li>
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-microchip text-primary me-2"></i> RAM:</span>
                                    <span className="text-muted">{server.ram || 'N/A'}</span>
                                  </li>
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-hdd-o text-primary me-2"></i> Storage:</span>
                                    <span className="text-muted">{server.disk || 'N/A'}</span>
                                  </li>
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-wifi text-primary me-2"></i> Bandwidth:</span>
                                    <span className="text-muted">{server.bandwidth || 'N/A'}</span>
                                  </li>
                                </ul>

                                <div className="mb-4">
                                  <h5 className="text-muted mb-3">Operating System</h5>
                                  <div className="card">
                                    <div className="card-body p-4">
                                      <div className="row align-items-center">
                                        <div className="col-auto">
                                          {/* OS Logo Placeholder */}
                                          <div
                                            style={{
                                              width: "40px",
                                              height: "40px",
                                              backgroundImage: `url('${server.os_logo_url || '/assets/images/os/linux.png'}')`,
                                              backgroundSize: "contain",
                                              backgroundPosition: "center",
                                              backgroundRepeat: "no-repeat"
                                            }}
                                          ></div>
                                        </div>
                                        <div className="col">
                                          <h6 className="mb-1">{server.os || 'Linux'}</h6>
                                          <div className="text-muted">{server.os_version || ''}</div>
                                        </div>
                                        <div className="col-auto">
                                          <button
                                            className="btn btn-primary"
                                            onClick={() => openModal(reinstallModal.title, reinstallModal.body, reinstallModal.footer)}
                                          >
                                            Change
                                          </button>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Billing Card */}
                          <div className="col-xl-4 col-lg-12 col-md-12 mb-4">
                            <div className="card shadow-sm h-100">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-credit-card me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Billing Information</h3>
                              </div>
                              <div className="card-body">
                                <h5 className="text-muted mb-3">Plan Details</h5>
                                <ul className="list-group list-group-flush mb-4">
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-tag text-primary me-2"></i> Plan:</span>
                                    <span className="text-muted fw-bold">{server.plan || 'N/A'}</span>
                                  </li>
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-money text-primary me-2"></i> Price:</span>
                                    <span className="text-muted fw-bold">{server.requirement_price || 'N/A'}</span>
                                  </li>
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-refresh text-primary me-2"></i> Billing Cycle:</span>
                                    <span className="text-muted">{getBillingCycleName(server.billing_cycle)}</span>
                                  </li>
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-calendar text-primary me-2"></i> Next Renewal:</span>
                                    <span className="text-muted">{server.next_renewal || 'N/A'}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-repeat text-primary me-2"></i> Auto-Renewal:</span>
                                    <div className="d-flex align-items-center">
                                      {isUpdatingAutoRenewal && (
                                        <div className="spinner-border spinner-border-sm me-2" role="status" style={{ width: '1rem', height: '1rem' }}>

                                        </div>
                                      )}
                                      <span
                                        className={`badge ${server.auto_renewal ? 'bg-success' : 'bg-danger'}`}
                                        style={{ cursor: isUpdatingAutoRenewal ? 'wait' : 'pointer' }}
                                        onClick={() => {
                                          if (isUpdatingAutoRenewal) return; // Prevent clicking while updating

                                          // Get the new state (opposite of current)
                                          const newAutoRenewalState = !server.auto_renewal;

                                          // Immediately update the UI to show the change
                                          setServer({...server, auto_renewal: newAutoRenewalState});

                                          // Set updating state to true to show loading
                                          setIsUpdatingAutoRenewal(true);

                                          console.log(`Auto-renewal changing to: ${newAutoRenewalState ? 'enabled' : 'disabled'}`);

                                          // Call the API to update auto-renewal status
                                          (async () => {
                                            try {
                                            const token = sessionStorage.getItem('token');
                                            const response = await fetch('/api.php?f=update_auto_renewal', {
                                              method: 'POST',
                                              headers: {
                                                'Content-Type': 'application/json'
                                              },
                                              body: JSON.stringify({
                                                token,
                                                server_id: server.id,
                                                auto_renewal: newAutoRenewalState
                                              })
                                            });

                                            const data = await response.json();
                                            console.log("Auto-renewal update response:", data);

                                            if (data.success) {
                                              // Update status message (could be shown as a toast notification)
                                              console.log(`Auto-renewal ${newAutoRenewalState ? 'enabled' : 'disabled'} successfully`);
                                            } else {
                                              // Show error message
                                              console.error('Error updating auto-renewal status:', data.error || data.message);

                                              // Revert checkbox state
                                              setServer({...server, auto_renewal: !newAutoRenewalState});

                                              // Show error message
                                              alert('Error updating auto-renewal status: ' + (data.error || data.message || 'Unknown error'));
                                            }
                                          } catch (error) {
                                            console.error('Error updating auto-renewal status:', error);

                                            // Revert checkbox state
                                            setServer({...server, auto_renewal: !newAutoRenewalState});

                                            // Show error message
                                            alert('Error updating auto-renewal status: ' + error.message);
                                          } finally {
                                            // Re-enable the badge
                                            setIsUpdatingAutoRenewal(false);
                                          }
                                          })();
                                        }}
                                      >
                                        {server.auto_renewal ? 'Enabled' : 'Disabled'}
                                      </span>
                                    </div>
                                  </li>
                                </ul>






                                <div className="mt-4 d-flex justify-content-between">
        <button
          className="btn btn-danger ms-2"
          onClick={() => openModal(terminateModal.title, terminateModal.body, terminateModal.footer)}
        >
          {terminateLoading ? (
            <>
              <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Terminating...
            </>
          ) : 'Terminate'}
        </button>
        <button className="btn btn-success">
                                    <i className="fa fa-refresh me-1"></i> Renew Service
                                  </button>
                                  <Link to="/billing/invoices" className="btn btn-info btn-sm">
                                    <i className="fa fa-file-text-o me-1"></i> View Invoices
                                  </Link>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Network Tab */}
                    {activeTab === 'network' && (
                      <div className="tab-pane active">
                        <div className="row">
                          {/* Network Configuration Card */}
                          <div className="col-xl-4 col-lg-6 col-md-12 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-globe me-2 text-primary" style={{paddingRight:'0.5rem'}}></i>Network Configuration</h3>
                              </div>
                              <div className="card-body">
                                <ul className="list-group list-group-flush">
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-sitemap me-2"></i> Main IP:</span>
                                    <span>{server.main_ip || 'N/A'}</span>
                                  </li>
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-sitemap me-2"></i> Gateway:</span>
                                    <span>{server.gateway || 'N/A'}</span>
                                  </li>
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-sitemap me-2"></i> Netmask:</span>
                                    <span>{server.netmask || 'N/A'}</span>
                                  </li>
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-wifi me-2"></i> Bandwidth:</span>
                                    <span>{server.bandwidth || 'N/A'}</span>
                                  </li>
                                </ul>


                              </div>
                            </div>
                          </div>

                          {/* IP Addresses Card */}
                          <div className="col-xl-8 col-lg-6 col-md-12 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-globe me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>IP Addresses</h3>
                                <div className="card-options">
                                <button 
  className="btn btn-sm btn-primary"
  onClick={toggleVpsIpUpgradeModal}
>
  <i className="fa fa-plus me-1"></i> Add IP Address
</button>
                                </div>
                              </div>
                              <div className="card-body">
                                <div className="table-responsive">
                                  <table className="table table-hover">
                                    <thead className="bg-light">
                                      <tr>
                                        <th>IP Address</th>
                                        <th>Type</th>
                                        <th>Gateway</th>
                                        <th>Netmask</th>
                                        <th>Reverse DNS</th>
                                        <th>Actions</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      <tr>
                                        <td>
                                          <div className="d-flex align-items-center">
                                            <span className="me-2" style={{paddingRight: '0.5rem'}}>{server.main_ip}</span>
                                            <span className="badge bg-primary ms-1">Primary</span>
                                          </div>
                                        </td>
                                        <td>Primary</td>
                                        <td>{server.gateway || 'N/A'}</td>
                                        <td>{server.netmask || 'N/A'}</td>
                                        <td>{server.rdns || <span className="text-muted">Not set</span>}</td>
                                        <td>
                                          <div className="btn-group" role="group">
<button
  className="btn btn-sm btn-primary"
  onClick={() => openRdnsModal(server.main_ip, server.rdns || '', server.rdns_id || null)}
>
  <i className="fa fa-edit me-1"></i> {server.rdns ? 'Edit rDNS' : 'Set rDNS'}
</button>
                                            {server.rdns && server.rdns_id && (
                                              <button
                                                className="btn btn-sm btn-danger"
                                                onClick={() => handleRdnsDelete(server.rdns_id)}
                                              >
                                                <i className="fa fa-trash me-1"></i> Delete rDNS
                                              </button>
                                            )}
                                          </div>
                                        </td>
                                      </tr>
                                      {server.additional_ips && server.additional_ips.map((ip, index) => (
                                        <tr key={index}>
                                          <td>
                                            <div className="d-flex align-items-center">
                                              <span className="me-2">{ip.address}</span>
                                              {ip.rdns && (
                                                <span className="badge bg-info ms-1" title={`rDNS: ${ip.rdns}`}>
                                                  <i className="fa fa-check me-1"></i> rDNS
                                                </span>
                                              )}
                                            </div>
                                          </td>
                                          <td>Additional</td>
                                          <td>{ip.gateway || server.gateway || 'N/A'}</td>
                                          <td>{ip.netmask || server.netmask || 'N/A'}</td>
                                          <td>{ip.rdns || <span className="text-muted">Not set</span>}</td>
                                          <td>
                                            <div className="btn-group" role="group">
                                              <button
                                                className="btn btn-sm btn-primary"
                                                onClick={() => openRdnsModal(ip.address, ip.rdns || '', ip.rdns_id || null)}
                                              >
                                                <i className="fa fa-edit me-1"></i> {ip.rdns ? 'Edit rDNS' : 'Set rDNS'}
                                              </button>
                                              {ip.rdns && ip.rdns_id && (
                                                <button
                                                  className="btn btn-sm btn-danger"
                                                  onClick={() => handleRdnsDelete(ip.rdns_id)}
                                                >
                                                  <i className="fa fa-trash me-1"></i> Delete rDNS
                                                </button>
                                              )}
                                              <button className="btn btn-sm btn-danger">
                                                <i className="fa fa-trash me-1"></i> Remove IP
                                              </button>
                                            </div>
                                          </td>
                                        </tr>
                                      ))}
                                    </tbody>
                                  </table>
                                </div>
                              </div>
                            </div>
                          </div>

                        </div>
                      </div>
                    )}



                    {/* Settings Tab */}
                    {activeTab === 'settings' && (
                      <div className="tab-pane active">
                        <div className="row">
                          {/* Server Settings Card */}
                          <div className="col-md-6 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-cog me-2 text-primary"></i>Server Settings</h3>
                              </div>
                              <div className="card-body">
                                <form onSubmit={handleUpdateHostname}>
                                  <div className="form-group mb-3">
                                    <label className="form-label">Server Hostname</label>
                                    <input
                                      type="text"
                                      className="form-control"
                                      defaultValue={server.hostname || server.name}
                                      placeholder="Server Hostname"
                                      onChange={(e) => setNewHostname(e.target.value)}
                                      required
                                    />
                                    <small className="form-text text-muted">
                                      This hostname identifies your server on the network.
                                    </small>
                                  </div>

                                  {hostnameUpdateResult && (
                                    <div className={`alert ${hostnameUpdateResult.error === 0 ? 'alert-success' : 'alert-danger'} mt-3`}>
                                      <i className={`fa ${hostnameUpdateResult.error === 0 ? 'fa-check-circle' : 'fa-exclamation-circle'} me-2`}></i>
                                      {hostnameUpdateResult.message}
                                    </div>
                                  )}

                                  <div className="d-flex justify-content-end">
                                    <button
                                      type="submit"
                                      className="btn btn-primary"
                                      disabled={hostnameUpdateLoading}
                                    >
                                      {hostnameUpdateLoading ? (
                                        <>
                                          <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                          Updating...
                                        </>
                                      ) : 'Save Settings'}
                                    </button>
                                  </div>
                                </form>
                              </div>
                            </div>
                          </div>

                          {/* Billing Settings Card */}
                          <div className="col-md-6 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">

                                <h3 className="card-title"><i className="fa fa-credit-card me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Billing Settings</h3>
                              </div>
                              <div className="card-body">
                                <form>
                  

                     

                                  <div className="form-group mb-3">

  <div className="d-flex align-items-center mb-2">
  <span
      className={`badge ${server.auto_renewal ? 'bg-success' : 'bg-danger'}`}
      style={{
        cursor: isUpdatingAutoRenewal ? 'wait' : 'pointer',
        padding: '8px 12px',
        fontSize: '14px'
      }}
      onClick={() => {
        if (isUpdatingAutoRenewal) return; // Prevent clicking while updating

        // Get the new state (opposite of current)
        const newAutoRenewalState = !server.auto_renewal;

        // Immediately update the UI to show the change
        setServer({...server, auto_renewal: newAutoRenewalState});

        // Set updating state to true to show loading
        setIsUpdatingAutoRenewal(true);

        // Set updating state to show the spinner

        console.log(`Auto-renewal changing to: ${newAutoRenewalState ? 'enabled' : 'disabled'}`);

        // Call the API to update auto-renewal status
        (async () => {
          try {
            const token = sessionStorage.getItem('token');
            const response = await fetch('/api.php?f=update_auto_renewal', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                token,
                server_id: server.id,
                auto_renewal: newAutoRenewalState
              })
            });

            const data = await response.json();
            console.log("Auto-renewal update response:", data);

            if (data.success) {
              // Success - no message needed
              console.log(`Auto-renewal ${newAutoRenewalState ? 'enabled' : 'disabled'} successfully`);
            } else {
              // Show error message
              console.error('Error updating auto-renewal status:', data.error || data.message);

              // Revert checkbox state
              setServer({...server, auto_renewal: !newAutoRenewalState});
            }
          } catch (error) {
            console.error('Error updating auto-renewal status:', error);

            // Revert badge state
            setServer({...server, auto_renewal: !newAutoRenewalState});
          } finally {
            // Re-enable the badge
            setIsUpdatingAutoRenewal(false);
          }
        })();
      }}
    >
      {server.auto_renewal ? 'Enabled' : 'Disabled'}
    </span>
  <label className="form-label">Auto-Renewal</label>

    {isUpdatingAutoRenewal && (
      <div className="spinner-border spinner-border-sm ms-2" role="status" style={{ width: '1rem', height: '1rem' }}>
      </div>
    )}
  </div>
  <small className="form-text text-muted d-block">
    When disabled, your server will be suspended after the billing period ends unless manually renewed.
    Click the badge to toggle auto-renewal.
  </small>
</div>

<div className="form-group mb-3">
  <div className="d-flex align-items-center mb-2">
  <span
      className={`badge ${server.use_credit ? 'bg-success' : 'bg-danger'}`}
      style={{
        cursor: isUpdatingUseCredit ? 'wait' : 'pointer',
        padding: '8px 12px',
        fontSize: '14px'
      }}
      onClick={() => {
        if (isUpdatingUseCredit) return; // Prevent clicking while updating

        // Get the new state (opposite of current)
        const newUseCreditState = !server.use_credit;

        // Immediately update the UI to show the change
        setServer({...server, use_credit: newUseCreditState});

        // Set updating state to true to show loading
        setIsUpdatingUseCredit(true);

        console.log(`Use credit changing to: ${newUseCreditState ? 'enabled' : 'disabled'}`);

        // Call the API to update use_credit status
        (async () => {
          try {
            const token = sessionStorage.getItem('token');
            const response = await fetch('/api.php?f=update_use_credit', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                token,
                server_id: server.id,
                use_credit: newUseCreditState
              })
            });

            const data = await response.json();
            console.log("Use credit update response:", data);

            if (data.success) {
              // Success - no message needed
              console.log(`Use credit ${newUseCreditState ? 'enabled' : 'disabled'} successfully`);
            } else {
              // Show error message
              console.error('Error updating use credit status:', data.error || data.message);

              // Revert checkbox state
              setServer({...server, use_credit: !newUseCreditState});
            }
          } catch (error) {
            console.error('Error updating use credit status:', error);

            // Revert badge state
            setServer({...server, use_credit: !newUseCreditState});
          } finally {
            // Re-enable the badge
            setIsUpdatingUseCredit(false);
          }
        })();
      }}
    >
      {server.use_credit ? 'Enabled' : 'Disabled'}
    </span>
    <label className="form-label">Use Account Credit for Renewals</label>

    {isUpdatingUseCredit && (
      <div className="spinner-border spinner-border-sm ms-2" role="status" style={{ width: '1rem', height: '1rem' }}>
      </div>
    )}
  </div>
  <small className="form-text text-muted d-block">
    When enabled, available account credit will be automatically applied to renewal invoices.
    Click the badge to toggle this setting.
  </small>
</div>

</form>
                              </div>
                            </div>
                          </div>

                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <VpsIpUpgradeModal
  isOpen={showVpsIpUpgradeModal}
  onClose={toggleVpsIpUpgradeModal}
  server={server}
  onSuccess={handleVpsIpUpgradeSuccess}
/>
      {/* Modal for various actions */}
      <MDBModal show={showModal} tabIndex='-1' staticBackdrop>
        <MDBModalDialog>
          <MDBModalContent>
            <MDBModalHeader>
              <MDBModalTitle>{modalTitle}</MDBModalTitle>
              <button type="button" className="btn-close" onClick={toggleModal}></button>
            </MDBModalHeader>
            <MDBModalBody>{modalBody}</MDBModalBody>
            <MDBModalFooter>{modalFooter}</MDBModalFooter>
          </MDBModalContent>
        </MDBModalDialog>
      </MDBModal>
    </>
  );
};

export default VpsServerDetailsView;