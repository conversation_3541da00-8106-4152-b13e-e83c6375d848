  import React, { useState, useEffect, useCallback, useRef } from "react";
  import { Outlet, Link, useParams, useNavigate, useLocation } from "react-router-dom";
  import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, AreaChart, Area } from 'recharts';
  import ReactDOM from 'react-dom/client';
  import {
    MDBModal,
    MDBModalDialog,
    MDBModalContent,
    MDBModalHeader,
    MDBModalTitle,
    MDBModalBody,
    MDBModalFooter,
  } from 'mdb-react-ui-kit';
  import BandwidthUpgradeModal from '../components/BandwidthUpgradeModal';
  import SubnetUpgradeModal from '../components/SubnetUpgradeModal';
  import RenewServerModal from '../components/RenewServerModal';
  import DiscountAlert from "../components/DiscountAlert";
  import StorageUpgradeModal from '../components/StorageUpgradeModal';

  const ServerDetailsView = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const [server, setServer] = useState(null);
    const [loading, setLoading] = useState(true);
    const [showPassword, setShowPassword] = useState(false);
    const [activeTab, setActiveTab] = useState('overview');
    const [modalShow, setModalShow] = useState(false);
    const [modalTitle, setModalTitle] = useState('');
    const [modalBody, setModalBody] = useState('');
    const [modalFooter, setModalFooter] = useState('');
    const [powerActionLoading, setPowerActionLoading] = useState(false);
    const [powerActionResult, setPowerActionResult] = useState(null);
    const [powerStatus, setPowerStatus] = useState('Unknown');
    const [powerStatusLoading, setPowerStatusLoading] = useState(false);
    const [labelFormError, setLabelFormError] = useState('');
    const [isUpdatingAutoRenewal, setIsUpdatingAutoRenewal] = useState(false);
    const [isUpdatingUseCredit, setIsUpdatingUseCredit] = useState(false);
    const [isUpdatingBillingCycle, setIsUpdatingBillingCycle] = useState(false);
    const [isTerminating, setIsTerminating] = useState(false);
    const [trafficData, setTrafficData] = useState([]);
    const [trafficPeriod, setTrafficPeriod] = useState('24h');
    const [loadingTraffic, setLoadingTraffic] = useState(false);
    const [networkStats, setNetworkStats] = useState({
      peakTraffic: 0,
      avgTraffic: 0,
      monthlyUsage: 0,
      packetsPerSec: 0
    });
    const [showStorageUpgradeModal, setShowStorageUpgradeModal] = useState(false);
    const toggleStorageUpgradeModal = () => setShowStorageUpgradeModal(!showStorageUpgradeModal);
  
    // Bandwidth upgrade modal state
    const [showBandwidthModal, setShowBandwidthModal] = useState(false);

    const fetchServerDetails = useCallback(async () => {
      try {
        const token = sessionStorage.getItem('token');
        const response = await fetch(`/api.php?f=manage_dedicated&id=${id}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ token })
        });
  
        const responseText = await response.text();
        console.log("Server details response:", responseText);
  
        try {
          const data = JSON.parse(responseText);
          console.log("Parsed server details:", data);
  
          if (data && data.length > 0) {
            const serverData = data[0];
            
            // Handle IPMI password fallback
            if (!serverData.ipmi_pass && (serverData.ipmi_root_pass || serverData.ipmi_user_pass)) {
              serverData.ipmi_pass = serverData.ipmi_root_pass || serverData.ipmi_user_pass;
            }
  
            // Log important fields for debugging
            console.log("Server data fetched:", {
              id: serverData.id,
              cpu_id: serverData.cpu_id,  // This is important for bandwidth restrictions
              cpu: serverData.cpu,
              bandwidth_id: serverData.bandwidth_id,
              billing_cycle: serverData.billing_cycle,
              payment_period: serverData.payment_period,
              monthly_price: serverData.monthly_price
            });
  
            setServer(serverData);
            return serverData; // Return the data in case caller needs it
          } else {
            console.error("No server data returned or format unexpected");
            return null;
          }
        } catch (parseError) {
          console.error("Error parsing server details response:", parseError);
          return null;
        }
      } catch (error) {
        console.error("Error fetching server details:", error);
        return null;
      }
    }, [id]);

    // Subnet upgrade modal state
    const [showSubnetModal, setShowSubnetModal] = useState(false);

    // Server renewal modal state
    const [showRenewModal, setShowRenewModal] = useState(false);

    // Handle successful bandwidth upgrade invoice generation
    const handleBandwidthUpgradeSuccess = (data) => {
      console.log("Bandwidth upgrade invoice success callback received:", data);
      const invoiceId = data.invoice_id || null;
    
      // Use the shared fetchServerDetails
      fetchServerDetails();
    
      setTimeout(() => {
        const goToInvoices = window.confirm("Would you like to view your invoices to complete the payment?");
        if (goToInvoices) {
          window.location.href = `/billing/invoices/${invoiceId}`;
        }
      }, 500);
    };

    // Handle successful subnet upgrade invoice generation
    const handleSubnetUpgradeSuccess = (data) => {
      console.log("Subnet upgrade invoice success callback received:", data);

      const invoiceId = data.invoice_id || 'Unknown';
      const totalAmount = data.total_amount ? parseFloat(data.total_amount).toFixed(2) : '0.00';
      const proratedAmount = data.prorated_amount ? parseFloat(data.prorated_amount).toFixed(2) : '0.00';
      const vatAmount = data.vat_amount ? parseFloat(data.vat_amount).toFixed(2) : '0.00';
      const vatRate = data.vat_rate || '0';


      fetchServerDetails();



      setTimeout(() => {
        const goToInvoices = window.confirm("Would you like to view your invoices to complete the payment?");
        if (goToInvoices) {
          window.location.href = `/billing/invoices/${invoiceId}`;
        }
      }, 500);
    };

    // Handle successful server renewal invoice generation
    const handleRenewalSuccess = (data) => {
      console.log("Server renewal invoice success callback received:", data);

      const invoiceId = data.invoice_id || 'Unknown';
      const totalAmount = data.total_amount ? parseFloat(data.total_amount).toFixed(2) : '0.00';
      const proratedAmount = data.prorated_amount ? parseFloat(data.prorated_amount).toFixed(2) : '0.00';
      const vatAmount = data.vat_amount ? parseFloat(data.vat_amount).toFixed(2) : '0.00';
      const vatRate = data.vat_rate || '0';


      // Refresh server details to show updated information

      fetchServerDetails();

      // Ask if user wants to view invoices
      setTimeout(() => {
        const goToInvoices = window.confirm("Would you like to view your invoices to complete the payment?");
        if (goToInvoices) {
          window.location.href = "/billing/invoices/";
        }
      }, 500);
    };



    // Sample bandwidth data for the graph
    const bandwidthData = [
      { time: '00:00', inbound: 2.3, outbound: 1.8 },
      { time: '02:00', inbound: 1.8, outbound: 1.2 },
      { time: '04:00', inbound: 1.2, outbound: 0.8 },
      { time: '06:00', inbound: 1.5, outbound: 0.9 },
      { time: '08:00', inbound: 3.5, outbound: 2.1 },
      { time: '10:00', inbound: 5.2, outbound: 3.8 },
      { time: '12:00', inbound: 4.8, outbound: 3.2 },
      { time: '14:00', inbound: 5.5, outbound: 4.1 },
      { time: '16:00', inbound: 6.2, outbound: 4.8 },
      { time: '18:00', inbound: 7.1, outbound: 5.2 },
      { time: '20:00', inbound: 4.5, outbound: 3.8 },
      { time: '22:00', inbound: 3.2, outbound: 2.5 },
    ];

    // Sample CPU usage data for the graph
    const cpuData = [
      { time: '00:00', usage: 15 },
      { time: '02:00', usage: 12 },
      { time: '04:00', usage: 10 },
      { time: '06:00', usage: 18 },
      { time: '08:00', usage: 35 },
      { time: '10:00', usage: 62 },
      { time: '12:00', usage: 48 },
      { time: '14:00', usage: 55 },
      { time: '16:00', usage: 72 },
      { time: '18:00', usage: 65 },
      { time: '20:00', usage: 45 },
      { time: '22:00', usage: 30 },
    ];

    // Sample memory usage data
    const memoryData = [
      { time: '00:00', usage: 45 },
      { time: '02:00', usage: 42 },
      { time: '04:00', usage: 40 },
      { time: '06:00', usage: 38 },
      { time: '08:00', usage: 55 },
      { time: '10:00', usage: 72 },
      { time: '12:00', usage: 65 },
      { time: '14:00', usage: 58 },
      { time: '16:00', usage: 62 },
      { time: '18:00', usage: 70 },
      { time: '20:00', usage: 55 },
      { time: '22:00', usage: 48 },
    ];

    // Sample disk IO data
    const diskData = [
      { time: '00:00', read: 12, write: 5 },
      { time: '02:00', read: 10, write: 3 },
      { time: '04:00', read: 8, write: 2 },
      { time: '06:00', read: 15, write: 7 },
      { time: '08:00', read: 25, write: 12 },
      { time: '10:00', read: 38, write: 15 },
      { time: '12:00', read: 30, write: 10 },
      { time: '14:00', read: 22, write: 8 },
      { time: '16:00', read: 35, write: 20 },
      { time: '18:00', read: 40, write: 22 },
      { time: '20:00', read: 28, write: 15 },
      { time: '22:00', read: 20, write: 8 },
    ];



    const handleLabelUpdate = async (e) => {
      e.preventDefault();
      const newLabel = e.target.elements.serverLabel.value.trim();
    
      if (!newLabel) {
        setLabelFormError('Server label cannot be empty');
        return;
      }
    
      setLabelFormError('');
    
      try {
        const token = sessionStorage.getItem('token');
        const response = await fetch('/api.php?f=update_server_label', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            token,
            server_id: server.id,
            label: newLabel
          })
        });
    
        const text = await response.text();
        console.log("Raw response:", text);
    
        let data;
        try {
          data = JSON.parse(text);
        } catch (e) {
          console.error("Failed to parse response:", e);
          throw new Error("Invalid server response");
        }
    
        if (data.success) {
          // Update local state
          setServer({...server, label: newLabel});
    
          // Use the shared fetchServerDetails
          fetchServerDetails();
    
          alert('Server label updated successfully');
        } else {
          setLabelFormError(data.error || 'Failed to update label');
        }
      } catch (error) {
        console.error("Error updating label:", error);
        setLabelFormError('Connection error - please try again');
      }
    };

    // Function to check server power status
    const checkServerPowerStatus = async () => {
      if (!server || !server.id) return;

      setPowerStatusLoading(true);
      try {
        const token = sessionStorage.getItem('token');
        console.log(`Checking power status for server ID: ${server.id}`);

        // Add a timestamp to prevent caching
        const timestamp = new Date().getTime();

        // Use the server_power.php endpoint with a 'status' action
        const requestUrl = `/server_power.php?_=${timestamp}`;
        console.log('Request URL:', requestUrl);

        const response = await fetch(requestUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          },
          body: JSON.stringify({
            token,
            server_id: server.id,
            action: 'status' // New action type for just checking status
          })
        });

        const responseText = await response.text();
        console.log('Raw API response:', responseText);

        try {
          const data = JSON.parse(responseText);
          console.log('Parsed power status response:', data);

          if (data.error === 0 && data.current_state) {
            setPowerStatus(data.current_state);
          } else {
            console.error('Error checking power status:', data.message || 'Unknown error');
            setPowerStatus('Unknown');
          }
        } catch (parseError) {
          console.error('Error parsing power status response:', parseError);
          setPowerStatus('Unknown');
        }
      } catch (error) {
        console.error('Error checking server power status:', error);
        setPowerStatus('Unknown');
      } finally {
        setPowerStatusLoading(false);
      }
    };

    useEffect(() => {
      const loadServerData = async () => {
        setLoading(true);
        await fetchServerDetails();
        setLoading(false);
      };
    
      loadServerData();
    }, [id, fetchServerDetails]);

    // Check power status when server data is loaded
    useEffect(() => {
      if (server && server.id) {
        checkServerPowerStatus();

        // Set up interval to check power status every 60 seconds
        const intervalId = setInterval(checkServerPowerStatus, 60000);

        // Clean up interval on component unmount
        return () => clearInterval(intervalId);
      }
    }, [server]);

    const togglePassword = () => {
      setShowPassword(!showPassword);
    };

    const copyToClipboard = (text) => {
      navigator.clipboard.writeText(text);
    };

    // Function to convert billing cycle value to human-readable format
    const getBillingCycleName = useCallback((period) => {
      // Convert payment_period to human-readable format
      const cycleValue = period ? parseInt(period) : 1;
      switch(cycleValue) {
        case 3: return "Quarterly";
        case 6: return "Semi-Annual";
        case 12: return "Annual";
        default: return "Monthly";
      }
    }, []);

    // Function to check if a subnet is an upgrade
    const isSubnetUpgrade = useCallback((selectedCIDR, currentCIDR) => {
      // A smaller CIDR number means a larger subnet
      // e.g., /28 (16 IPs) is larger than /29 (8 IPs)
      return selectedCIDR < currentCIDR;
    }, []);

    // Modified IP Configuration Modal
    const createIpConfigModal = useCallback(() => {
      // If server isn't loaded yet, return the default modal
      if (!server) return {
        title: <>IP Configuration</>,
        footer: <><button className="btn btn-success" onClick={() => setModalShow(false)}>Add IP Resources</button></>
      };

      // Extract the current CIDR from server data
      const currentSubnet = server.subnet;
      const currentCIDRMatch = currentSubnet.match(/\/(\d+)/);
      const currentCIDR = currentCIDRMatch ? parseInt(currentCIDRMatch[1]) : 29; // Default to 29 if not found

      return {
        title: <>IP Configuration</>,
        body: <>
          <div className="form-group">
            <label className="form-label mb-2">Select Upgrade Option:</label>
            <select name="ip_option" className="form-control custom-select mb-3">
              <option value="31" disabled={!isSubnetUpgrade(31, currentCIDR)}>Additional IPv4 Address (+€3.00/mo)</option>
              <option value="29" disabled={!isSubnetUpgrade(29, currentCIDR)}>
                /29 IPv4 Subnet - 5 IPs (+€15.00/mo)
                {!isSubnetUpgrade(29, currentCIDR) && " - Not an upgrade"}
              </option>
              <option value="28" disabled={!isSubnetUpgrade(28, currentCIDR)}>
                /28 IPv4 Subnet - 13 IPs (+€30.00/mo)
                {!isSubnetUpgrade(28, currentCIDR) && " - Not an upgrade"}
              </option>
              <option value="27" disabled={!isSubnetUpgrade(27, currentCIDR)}>
                /27 IPv4 Subnet - 29 IPs (+€60.00/mo)
                {!isSubnetUpgrade(27, currentCIDR) && " - Not an upgrade"}
              </option>
              <option value="26" disabled={!isSubnetUpgrade(26, currentCIDR)}>
                /26 IPv4 Subnet - 63 IPs (+€120.00/mo)
                {!isSubnetUpgrade(26, currentCIDR) && " - Not an upgrade"}
              </option>
            </select>

            <div className="alert alert-info mb-3">
              <i className="fa fa-info-circle me-2"></i>
              You can only select larger subnet sizes than your current one ({currentSubnet}).
              IP allocation is usually processed within 10 minutes. Configuration instructions will be provided in your client area.
            </div>

            <div className="alert alert-danger mb-0">
              <i className="fa fa-exclamation-triangle me-2" ></i>
              <strong>WARNING:</strong> This function makes your server inaccessible from the old IP addresses.
            </div>
          </div>
        </>,
        footer: <><button className="btn btn-success" onClick={() => setModalShow(false)}>Add IP Resources</button></>
      };
    }, [server, isSubnetUpgrade]);

    const terminateModal = {
      title: <>Terminate Server</>,
      body: <>
        <div className="form-group">
          <div className="alert alert-danger mb-3">
            <i className="fa fa-exclamation-triangle me-2" style={{paddingRight: '0.5rem'}}></i>
            <strong>WARNING:</strong> Terminating the server will permanently delete all data. This action cannot be undone.
          </div>
          <p>Please confirm that you want to terminate server <strong>{server?.label}</strong>.</p>

          {/* Termination timing options */}
          <div className="mb-3">
            <label className="form-label">When would you like to terminate the server?</label>
            <div className="form-check mb-2">
              <input
                type="radio"
                className="form-check-input"
                id="terminateImmediately"
                name="terminationTiming"
                value="immediately"
                  style={{
          marginRight: '5px',
          marginLeft: '0',
          marginTop: '0',
          marginBottom: '0',
          padding: '0',
          position: 'relative',
          float: 'none'
        }}
                defaultChecked
              />
              <label className="form-check-label" htmlFor="terminateImmediately">
                Terminate immediately
              </label>
            </div>
            <div className="form-check">
              <input
                type="radio"
                className="form-check-input"
                id="terminateEndOfBilling"
                name="terminationTiming"
                value="end-of-billing"
                  style={{
          marginRight: '5px',
          marginLeft: '0',
          marginTop: '0',
          marginBottom: '0',
          padding: '0',
          position: 'relative',
          float: 'none'
        }}
              />
              <label className="form-check-label" htmlFor="terminateEndOfBilling">
                Terminate at the end of billing period {server?.expires && `(${server.expires})`}
              </label>
            </div>
          </div>

          {/* Reason for termination */}
          <div className="mb-3">
            <label htmlFor="terminationReason" className="form-label">Reason for termination <span className="text-danger">*</span></label>
            <textarea
              className="form-control"
              id="terminationReason"
              rows="3"
              placeholder="Please tell us why you're terminating this server..."
              required
            ></textarea>
            <div className="invalid-feedback" id="reasonFeedback" style={{display: 'none'}}>
              Please provide a reason for termination.
            </div>
          </div>

          <div className="form-check mb-3">
            <input type="checkbox" className="form-check-input" id="confirmTermination"   style={{
          marginRight: '5px',
          marginLeft: '0',
          marginTop: '0',
          marginBottom: '0',
          padding: '0',
          position: 'relative',
          float: 'none'
        }} />
            <label className="form-check-label" htmlFor="confirmTermination">
              All data on this server will be permanently deleted.
            </label>
          </div>
        </div>
      </>,
      footer: <>
        <button className="btn btn-danger" onClick={() => setModalShow(false)}>Cancel</button>
        <button
          className="btn btn-secondary ms-2"
          disabled={isTerminating}
          onClick={() => {
            const checkbox = document.getElementById('confirmTermination');
            const reasonTextarea = document.getElementById('terminationReason');
            const reasonFeedback = document.getElementById('reasonFeedback');
            const terminationReason = reasonTextarea.value.trim();

            // Check confirmation checkbox
            if (!checkbox || !checkbox.checked) {
              alert('Please check the confirmation checkbox to proceed.');
              return;
            }

            // Validate reason field
            if (!terminationReason) {
              reasonFeedback.style.display = 'block';
              reasonTextarea.classList.add('is-invalid');
              return;
            } else {
              reasonFeedback.style.display = 'none';
              reasonTextarea.classList.remove('is-invalid');
            }

            // Get the selected termination timing
            const terminateImmediately = document.getElementById('terminateImmediately').checked;
            const terminationTiming = terminateImmediately ? 'immediately' : 'end-of-billing';

            // Handle termination with the collected data
            console.log('Server termination requested:', {
              server: server?.label,
              timing: terminationTiming,
              reason: terminationReason
            });

            // Call the API to perform the termination
            handleServerTermination(server?.id, terminationTiming, terminationReason);

            // Close the modal
            setModalShow(false);
          }}
        >
          {isTerminating ? (
            <>
              <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Processing...
            </>
          ) : (
            <>Terminate Server</>
          )}
        </button>
      </>
    };

  // First, add debug logging to see what we're working with
  useEffect(() => {
    if (server) {
      console.log("Server data:", {
        main_ip: server.main_ip,
        additional_ips: server.additional_ips,
        additional_ips_type: typeof server.additional_ips
      });
    }
  }, [server]);

  // Function to handle server termination
  const handleServerTermination = async (serverId, timing, reason) => {
    if (!serverId) {
      alert('Server ID is missing');
      return;
    }

    try {
      setIsTerminating(true);
      const token = sessionStorage.getItem('token');

      console.log("Terminating server with:", {
        server_id: serverId,
        timing: timing,
        reason: reason
      });

      const response = await fetch('/server_terminate.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token,
          server_id: serverId,
          timing: timing,
          reason: reason
        })
      });

      const text = await response.text();
      console.log("Raw termination response:", text);

      let data;
      try {
        data = JSON.parse(text);
      } catch (e) {
        console.error("Failed to parse termination response:", e);
        throw new Error("Invalid server response");
      }

      if (data.error === 0) {
        // Show success message
        alert(`Server termination ${timing === 'immediately' ? 'completed' : 'scheduled'}. ${data.details}`);

        // If immediate termination, redirect to services page
        if (timing === 'immediately') {
          navigate('/services');
        } else {
          // Use the shared fetchServerDetails
          fetchServerDetails();
        }
      } else {
        // Show error message
        alert(`Failed to terminate server: ${data.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.error("Error terminating server:", error);
      alert('Connection error - please try again');
    } finally {
      setIsTerminating(false);
    }
  };

  const updateServerLabel = async (newLabel) => {
    try {
      setLoading(true);

      const token = sessionStorage.getItem('token');
      const response = await fetch('/api.php?f=update_server_label', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token,
          server_id: server.id,
          label: newLabel
        })
      });

      const data = await response.json();

      if (data.success) {
        // Update the local server state with the new label
        setServer({
          ...server,
          label: newLabel
        });
        // Show success notification
        alert('Server label updated successfully');
        return true;
      } else {
        // Show error notification
        alert(`Failed to update server label: ${data.error}`);
        return false;
      }
    } catch (error) {
      console.error('Error updating server label:', error);
      alert('An error occurred while updating the server label');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const getAllUsableIpsFromSubnet = (server) => {
    let allIps = [];

    // Process main subnet
    if (server.main_ip) {
      console.log("Processing main subnet:", server.main_ip);
      const mainSubnetIps = getIpsFromSubnet(server.main_ip);

      allIps = allIps.concat(mainSubnetIps.map(ip => ({
        ip,
        source: server.main_ip,
        isMain: mainSubnetIps.indexOf(ip) === 0
      })));
    }

    // Process additional subnets with more robust handling
    if (server.additional_ips) {
      console.log("Raw additional_ips value:", server.additional_ips);
      console.log("Type of additional_ips:", typeof server.additional_ips);

      let additionalSubnets = [];

      if (typeof server.additional_ips === 'string') {
        // Split and trim each subnet
        additionalSubnets = server.additional_ips.split(',')
          .map(subnet => subnet.trim())
          .filter(subnet => subnet); // Remove empty entries
      } else if (Array.isArray(server.additional_ips)) {
        // If it's already an array, use it directly
        additionalSubnets = server.additional_ips;
      }

      console.log("Processed additional subnets:", additionalSubnets);

      additionalSubnets.forEach(subnet => {
        console.log("Processing additional subnet:", subnet);
        const additionalIps = getIpsFromSubnet(subnet);
        console.log(`Generated ${additionalIps.length} IPs for subnet ${subnet}`);

        allIps = allIps.concat(additionalIps.map(ip => ({
          ip,
          source: subnet,
          isMain: false
        })));
      });
    }

    console.log(`Total IPs found: ${allIps.length}`);
    return allIps;
  };

  // Helper function to get IPs from a subnet
  const getIpsFromSubnet = (subnet) => {
    console.log(`Calculating IPs for subnet: ${subnet}`);

    if (!subnet) return [];

    try {
      const parts = subnet.split('/');
      if (parts.length !== 2) return [];

      const baseIp = parts[0];
      const cidr = parseInt(parts[1]);

      const ipParts = baseIp.split('.');
      if (ipParts.length !== 4) return [];

      const ipOctets = ipParts.map(part => parseInt(part));
      const ips = [];

      // Special handling for different CIDR sizes
      if (cidr === 31) {
        // /31 networks have 2 usable IPs (RFC 3021)
        // Return both IPs in the subnet
        const newParts2 = [...ipOctets];
        newParts2[3] = newParts2[3] + 1;


        ips.push(newParts2.join('.'));
      }
      else if (cidr === 32) {
        // /32 is a single host address
        ips.push(ipOctets.join('.'));
      }
      else {
        // Standard subnets
        const hostBits = 32 - cidr;
        const totalHosts = Math.pow(2, hostBits);

        // Get usable IPs (skip network address and broadcast address)
        for (let i = 2; i < totalHosts - 1 && i < 254; i++) {
          const newParts = [...ipOctets];

          // Handle different subnet sizes
          if (cidr >= 24) {
            // For /24 and smaller subnets, just increment the last octet
            newParts[3] = ipOctets[3] + i;
          } else if (cidr >= 16) {
            // For /16 to /23 subnets
            const lastOctet = ipOctets[3];
            const thirdOctet = ipOctets[2];

            const newLastOctet = (lastOctet + i) % 256;
            newParts[3] = newLastOctet;

            // Handle overflow to third octet
            if (lastOctet + i >= 256) {
              newParts[2] = thirdOctet + Math.floor((lastOctet + i) / 256);
            }
          }

          ips.push(newParts.join('.'));

          // Limit the number of IPs to display for very large subnets
          if (ips.length >= 100) break;
        }
      }

      console.log(`Generated ${ips.length} IPs for subnet ${subnet}`);
      return ips;
    } catch (error) {
      console.error("Error calculating IPs from subnet:", error);
      return [];
    }
  };

    // Function to execute power actions (on, off, reboot)
    const executeServerPowerAction = async (action) => {
      if (!server || !server.id) return;

      setPowerActionLoading(true);
      setPowerActionResult(null);

      try {
        const token = sessionStorage.getItem('token');
        console.log(`Sending power ${action} request for server ID: ${server.id}`);

        // Log the request payload
        const payload = {
          token,
          server_id: server.id,
          action
        };
        console.log('Request payload:', payload);

        // Add a timestamp to prevent caching
        const timestamp = new Date().getTime();

        // Log the full request URL - use the new dedicated endpoint
        const requestUrl = `/server_power.php?_=${timestamp}`;
        console.log('Request URL:', requestUrl);

        // Make the request with additional debugging
        console.log('Sending fetch request...');
        const response = await fetch(requestUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          },
          body: JSON.stringify(payload)
        });

        console.log('Fetch response received:', response);
        console.log('Response status:', response.status);
        console.log('Response headers:', [...response.headers.entries()]);

        // Log the raw response
        const responseText = await response.text();
        console.log('Raw API response:', responseText);

        // Try to parse the response as JSON
        let data;
        try {
          // Check if the response is empty
          if (!responseText.trim()) {
            console.error('Empty API response');

            // Create an error response
            data = {
              error: 1,
              message: 'Empty API response',
              details: 'The server returned an empty response'
            };
          } else {
            // Try to parse the JSON response
            data = JSON.parse(responseText);
            console.log('Parsed API response:', data);

            // Validate the response structure
            if (typeof data !== 'object' || data === null) {
              console.error('Invalid API response structure:', data);
              data = {
                error: 1,
                message: 'Invalid API response structure',
                details: 'The server returned an invalid response structure'
              };
            }
          }
        } catch (parseError) {
          console.error('Error parsing API response:', parseError);

          // Create a fallback response object
          data = {
            error: 1,
            message: 'Invalid API response format',
            details: `Failed to parse response: ${parseError.message}\n\nRaw response: ${responseText}`
          };
        }

        // Use the actual API response without simulation
        if (data.error !== 0) {
          console.error(`Server ${action} command failed:`, data);
        }

        setPowerActionResult(data);
// Inside the success handling
if (data.error === 0) {
  console.log(`Server ${action} command successful:`, data);

  setTimeout(() => {
    setModalShow(false);
    setPowerActionResult(null);
    setPowerActionLoading(false);

    if (typeof toast !== 'undefined') {
      toast.success(`Server ${action} command executed successfully`);
    }
  }, 2000);

  // Refresh server data after a short delay
  setTimeout(() => {
    // Use the shared fetchServerDetails
    fetchServerDetails().then(() => {
      // Also refresh the power status
      checkServerPowerStatus();
    });
  }, 5000);
}
      } catch (error) {
        console.error(`Error executing server ${action} command:`, error);

        // Create a detailed error message
        let errorDetails = `Error: ${error.message}`;
        if (error.stack) {
          errorDetails += `\n\nStack trace: ${error.stack}`;
        }

        // Show the actual error
        setPowerActionResult({
          error: 1,
          message: `Failed to execute ${action} command: ${error.message}`,
          details: errorDetails
        });
      } finally {
        setPowerActionLoading(false);
      }
    };

    // The openModal function has been moved after all its dependencies are defined

    // Modal content templates
    const rebootModal = {
      title: <>Reboot Server</>,
      body: <>
        <div className="form-group">
          <div className="alert alert-warning mb-0">
            <i className="fa fa-info-circle me-2"style={{paddingRight: '0.5rem'}}></i>
            After rebooting, please allow up to 10 minutes for the server to become accessible on SSH. If you can't access your server after 10 minutes, please use the OOB console to debug it remotely.
          </div>
          <div className="alert alert-danger mt-3 mb-0">
            <i className="fa fa-exclamation-triangle me-2" style={{paddingRight: '0.5rem'}}></i>
            <strong>WARNING:</strong> This action leads to downtime.
          </div>

          {powerActionLoading && (
            <div className="alert alert-info mt-3">
              <div className="d-flex align-items-center">
                <div className="spinner-border spinner-border-sm me-2" role="status">

                </div>
                Sending reboot command to server...
              </div>
            </div>
          )}

          {powerActionResult && (
            <div className={`alert ${powerActionResult.error === 0 ? 'alert-success' : 'alert-danger'} mt-3`}>
              <i className={`fa ${powerActionResult.error === 0 ? 'fa-check-circle' : 'fa-exclamation-circle'} me-2`}></i>
              {powerActionResult.message}
              {powerActionResult.details && (
                <div className="mt-2 small">
                  <strong>Details:</strong><br />
                  <pre className="mb-0">{powerActionResult.details}</pre>
                </div>
              )}
            </div>
          )}
        </div>
      </>,
      footer: <>
        <button
          className="btn btn-secondary"
          onClick={() => setModalShow(false)}
        >
          Close
        </button>
        <button
          className="btn btn-warning ms-2"
          onClick={() => executeServerPowerAction('reboot')}
          disabled={powerActionLoading}
        >
          {powerActionLoading ? (
            <>
              <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Processing...
            </>
          ) : (
            <>Reboot</>
          )}
        </button>
      </>
    };

  const IdracConsoleLauncher = ({ ipmiAddress, username, password, version }) => {
    const [launching, setLaunching] = useState(false);
    const [showInstructions, setShowInstructions] = useState(false);

    // Generate JNLP file content for iDRAC
    const generateJnlpContent = () => {
      // Base URL for iDRAC
      const baseUrl = ipmiAddress.startsWith('http')
        ? ipmiAddress.replace(/^https?:\/\//, '')
        : ipmiAddress;

      // Different JNLP configuration based on iDRAC version
      if (version === 9) {
        return `<?xml version="1.0" encoding="UTF-8"?>
  <jnlp spec="1.0+" codebase="https://${baseUrl}:443">
    <information>
      <title>Virtual Console Client</title>
      <vendor>Dell Inc.</vendor>
      <description>Virtual Console Client for iDRAC9</description>
    </information>
    <application-desc main-class="com.avocent.idrac.kvm.Main">
      <argument>IP=${baseUrl}</argument>
      <argument>JNLPSTR=JViewer</argument>
      <argument>JNLPNAME=JViewer.jnlp</argument>
      ${username ? `<argument>user=${username}</argument>` : ''}
      ${password ? `<argument>passwd=${password}</argument>` : ''}
      <argument>kmport=5900</argument>
      <argument>vport=5900</argument>
      <argument>apcp=1</argument>
      <argument>version=2</argument>
    </application-desc>
    <security>
      <all-permissions/>
    </security>
    <resources>
      <j2se version="1.8+" initial-heap-size="512m" max-heap-size="1024m"/>
      <jar href="https://${baseUrl}:443/software/avctKVM.jar" download="eager" main="true"/>
    </resources>
    <resources os="Windows" arch="x86">
      <nativelib href="https://${baseUrl}:443/software/avctKVMIOWin32.jar" download="eager"/>
    </resources>
    <resources os="Windows" arch="amd64">
      <nativelib href="https://${baseUrl}:443/software/avctKVMIOWin64.jar" download="eager"/>
    </resources>
    <resources os="Windows" arch="x86_64">
      <nativelib href="https://${baseUrl}:443/software/avctKVMIOWin64.jar" download="eager"/>
    </resources>
    <resources os="Linux" arch="x86">
      <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
    </resources>
    <resources os="Linux" arch="i386">
      <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
    </resources>
    <resources os="Linux" arch="i586">
      <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
    </resources>
    <resources os="Linux" arch="i686">
      <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
    </resources>
    <resources os="Linux" arch="amd64">
      <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux64.jar" download="eager"/>
    </resources>
    <resources os="Linux" arch="x86_64">
      <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux64.jar" download="eager"/>
    </resources>
    <resources os="Mac OS X" arch="x86_64">
      <nativelib href="https://${baseUrl}:443/software/avctKVMIOMac64.jar" download="eager"/>
    </resources>
  </jnlp>`;
      } else {
        // Default to iDRAC 8 format
        return `<?xml version="1.0" encoding="UTF-8"?>
  <jnlp spec="1.0+" codebase="https://${baseUrl}:443">
    <information>
      <title>iDRAC8 Virtual Console Client</title>
      <vendor>Dell Inc.</vendor>
      <description>iDRAC8 Virtual Console Client</description>
    </information>
    <application-desc main-class="com.avocent.idrac.kvm.Main">
      <argument>ip=${baseUrl}</argument>
      ${username ? `<argument>user=${username}</argument>` : ''}
      ${password ? `<argument>passwd=${password}</argument>` : ''}
      <argument>kmport=5900</argument>
      <argument>vport=5900</argument>
      <argument>title=iDRAC8 Virtual Console Client</argument>
      <argument>helpurl=https://${baseUrl}:443/help/contents.html</argument>
    </application-desc>
    <security>
      <all-permissions/>
    </security>
    <resources>
      <j2se version="1.6.0_24+" initial-heap-size="512m" max-heap-size="1024m"/>
      <jar href="https://${baseUrl}:443/software/avctKVM.jar" download="eager" main="true"/>
    </resources>
    <resources os="Windows" arch="x86">
      <nativelib href="https://${baseUrl}:443/software/avctKVMIOWin32.jar" download="eager"/>
    </resources>
    <resources os="Windows" arch="amd64">
      <nativelib href="https://${baseUrl}:443/software/avctKVMIOWin64.jar" download="eager"/>
    </resources>
    <resources os="Windows" arch="x86_64">
      <nativelib href="https://${baseUrl}:443/software/avctKVMIOWin64.jar" download="eager"/>
    </resources>
    <resources os="Linux" arch="x86">
      <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
    </resources>
    <resources os="Linux" arch="i386">
      <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
    </resources>
    <resources os="Linux" arch="i586">
      <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
    </resources>
    <resources os="Linux" arch="i686">
      <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
    </resources>
    <resources os="Linux" arch="amd64">
      <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux64.jar" download="eager"/>
    </resources>
    <resources os="Linux" arch="x86_64">
      <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux64.jar" download="eager"/>
    </resources>
    <resources os="Mac OS X" arch="x86_64">
      <nativelib href="https://${baseUrl}:443/software/avctKVMIOMac64.jar" download="eager"/>
    </resources>
  </jnlp>`;
      }
    };

    // Download JNLP file function
    const downloadJnlpFile = () => {
      setLaunching(true);

      try {
        const jnlpContent = generateJnlpContent();
        const blob = new Blob([jnlpContent], { type: 'application/x-java-jnlp-file' });
        const url = URL.createObjectURL(blob);

        // Create temporary link element to download the file
        const link = document.createElement('a');
        link.href = url;
        link.download = `idrac${version}-console.jnlp`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        setTimeout(() => {
          URL.revokeObjectURL(url);
          setLaunching(false);
        }, 1000);
      } catch (error) {
        console.error('Error generating JNLP file:', error);
        setLaunching(false);
      }
    };

    if (!ipmiAddress) return null;

    return (
      <div className="mt-1">
        <div className="flex flex-wrap items-center gap-2">
          <button
            onClick={downloadJnlpFile}
            disabled={launching}
            className={`flex items-center px-2 py-1 text-xs font-medium rounded transition-colors
              ${launching ? 'bg-gray-200 text-gray-500' : 'bg-blue-100 hover:bg-blue-200 text-blue-700'}`}
          >
            <svg className="w-3 h-3 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 17L15 17" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              <path d="M12 6V13" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              <path d="M12 13L15 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              <path d="M12 13L9 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              <path d="M3 15V16C3 18.2091 4.79086 20 7 20H17C19.2091 20 21 18.2091 21 16V15" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            </svg>
            {launching ? 'Generating...' : `Launch Java Console`}
          </button>

          <button
            onClick={() => setShowInstructions(!showInstructions)}
            className="flex items-center px-2 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded text-xs transition-colors"
          >
            <svg className="w-3 h-3 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
              <path d="M12 7V13" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              <circle cx="12" cy="17" r="1" fill="currentColor"/>
            </svg>
            {showInstructions ? 'Hide Help' : 'Help'}
          </button>
        </div>

        {showInstructions && (
          <div className="mt-2 p-3 bg-gray-50 rounded-md text-xs">
            <h4 className="font-medium mb-1">How to use iDRAC {version} Java Console:</h4>
            <ol className="list-decimal pl-4 space-y-1">
              <li>Download and install Java if you haven't already
                (<a href="https://www.java.com/download/" target="_blank" rel="noopener noreferrer"
                  className="text-blue-600 hover:underline">www.java.com</a>)</li>
              <li>Save the .jnlp file when prompted</li>
              <li>Right-click the downloaded file and select "Open with" → "Java Web Start"</li>
              <li>If prompted with security warnings, click "Run" to continue</li>
              <li>The iDRAC console should load with your credentials auto-filled</li>
            </ol>
            <p className="mt-2 text-gray-600">Note: You may need to add the iDRAC IP to the Java security exception site list in your Java Control Panel.</p>
          </div>
        )}
      </div>
    );
  };

  const handleStorageUpgradeSuccess = (data) => {
    console.log('Storage upgrade invoice generated successfully:', data);
    
    const invoiceId = data.invoice_id || null; // Add this line
    
    setTimeout(() => {
      const goToInvoices = window.confirm("Would you like to view your invoices to complete the payment?");
      if (goToInvoices) {
        // Use specific invoice ID if available
        window.location.href = invoiceId ? `/billing/invoices/${invoiceId}` : `/billing/invoices/`;
      }
    }, 500);
    
    setTimeout(() => {
      fetchServerDetails();
    }, 2000);
  };

  // Component for iDRAC auto-detection
  const IdracAutoDetectConsole = ({ ipmiAddress, username, password, selectedItem }) => {
    const [idracVersion, setIdracVersion] = useState(null);
    const [detecting, setDetecting] = useState(true);

    useEffect(() => {
      // Auto-detect iDRAC version when component mounts
      if (ipmiAddress) {
        setDetecting(true);

        // Try to extract version from label if available
        const detectedFromLabel = selectedItem?.label?.toLowerCase().match(/idrac\s*(\d+)/i);
        if (detectedFromLabel && (detectedFromLabel[1] === '8' || detectedFromLabel[1] === '9')) {
          setIdracVersion(parseInt(detectedFromLabel[1]));
          setDetecting(false);
          return;
        }

        // Try to detect from notes
        const detectedFromNotes = selectedItem?.notes?.toLowerCase().match(/idrac\s*(\d+)/i);
        if (detectedFromNotes && (detectedFromNotes[1] === '8' || detectedFromNotes[1] === '9')) {
          setIdracVersion(parseInt(detectedFromNotes[1]));
          setDetecting(false);
          return;
        }

        // Try to detect from model information (Dell servers with specific generations)
        const modelInfo = [
          { pattern: /r[0-9]40/i, version: 9 },    // R740, R640, R440, etc. = iDRAC 9
          { pattern: /r[0-9]30/i, version: 8 },    // R730, R630, R430, etc. = iDRAC 8
          { pattern: /r[0-9]20/i, version: 7 },    // R720, R620, R520, etc. = iDRAC 7
          { pattern: /poweredge\s*1[4-9]/i, version: 9 },  // PowerEdge 14G+ = iDRAC 9
          { pattern: /poweredge\s*1[23]/i, version: 8 },   // PowerEdge 12G-13G = iDRAC 8
          { pattern: /g1[4-9]/i, version: 9 },     // Dell G14+ servers = iDRAC 9
          { pattern: /g1[23]/i, version: 8 },      // Dell G12-G13 servers = iDRAC 8
        ];

        // Check against server label and notes
        const serverInfo = (selectedItem?.label || '') + ' ' + (selectedItem?.cpu || '') + ' ' + (selectedItem?.notes || '');

        for (const model of modelInfo) {
          if (model.pattern.test(serverInfo)) {
            setIdracVersion(model.version);
            setDetecting(false);
            return;
          }
        }

        // Default to iDRAC 8 if no detection method succeeds
        setIdracVersion(8);
        setDetecting(false);
      }
    }, [ipmiAddress, selectedItem]);

    if (!ipmiAddress) return null;

    // Only show for iDRAC 8 or 9 (iDRAC 7 and earlier used different mechanisms)
    if (idracVersion !== 8 && idracVersion !== 9) return null;

    return (
      <div className="mt-2 border-t pt-2">
        <div className="text-xs text-gray-500 mb-1">Launch iDRAC Console</div>

        {detecting ? (
          <div className="flex items-center text-xs text-gray-500">
            <i className="fa fa-refresh fa-spin mr-1" style={{width: '12px', height: '12px', marginRight: '4px'}}></i>
            Detecting iDRAC version...
          </div>
        ) : (
          <IdracConsoleLauncher
            ipmiAddress={ipmiAddress}
            username={username}
            password={password}
            version={idracVersion}
          />
        )}
      </div>
    );
  };

    const startModal = {
      title: <>Turn On Server</>,
      body: <>
        <div className="form-group">
          <div className="alert alert-warning mb-0">
            <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
            This action may take up to 10 minutes. If the server is not online within this time frame, please check the console for further details.
          </div>
          <div className="alert alert-danger mt-3 mb-0">
            <i className="fa fa-exclamation-triangle me-2" style={{paddingRight: '0.5rem'}}></i>
            <strong>WARNING:</strong> This action may take up to 10 minutes to complete.
          </div>

          {powerActionLoading && (
            <div className="alert alert-info mt-3">
              <div className="d-flex align-items-center">
                <div className="spinner-border spinner-border-sm me-2" role="status">

                </div>
                Sending power on command to server...
              </div>
            </div>
          )}

          {powerActionResult && (
            <div className={`alert ${powerActionResult.error === 0 ? 'alert-success' : 'alert-danger'} mt-3`}>
              <i className={`fa ${powerActionResult.error === 0 ? 'fa-check-circle' : 'fa-exclamation-circle'} me-2`}></i>
              {powerActionResult.message}
              {powerActionResult.details && (
                <div className="mt-2 small">
                  <strong>Details:</strong><br />
                  <pre className="mb-0">{powerActionResult.details}</pre>
                </div>
              )}
            </div>
          )}
        </div>
      </>,
      footer: <>
        <button
          className="btn btn-secondary"
          onClick={() => setModalShow(false)}
        >
          Close
        </button>
        <button
          className="btn btn-success ms-2"
          onClick={() => executeServerPowerAction('on')}
          disabled={powerActionLoading}
        >
          {powerActionLoading ? (
            <>
              <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Processing...
            </>
          ) : (
            <>Power On</>
          )}
        </button>
      </>
    };

    const shutdownModal = {
      title: <>Shutdown Server</>,
      body: <>
        <div className="form-group">
          <div className="alert alert-warning mb-0">
            <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
            This action will shut down your server, and you will need to manually restart it afterward.
          </div>
          <div className="alert alert-danger mt-3 mb-0">
            <i className="fa fa-exclamation-triangle me-2" style={{paddingRight: '0.5rem'}}></i>
            <strong>WARNING:</strong> This action leads to downtime.
          </div>

          {powerActionLoading && (
            <div className="alert alert-info mt-3">
              <div className="d-flex align-items-center">
                <div className="spinner-border spinner-border-sm me-2" role="status">

                </div>
                Sending shutdown command to server...
              </div>
            </div>
          )}

          {powerActionResult && (
            <div className={`alert ${powerActionResult.error === 0 ? 'alert-success' : 'alert-danger'} mt-3`}>
              <i className={`fa ${powerActionResult.error === 0 ? 'fa-check-circle' : 'fa-exclamation-circle'} me-2`}></i>
              {powerActionResult.message}
              {powerActionResult.details && (
                <div className="mt-2 small">
                  <strong>Details:</strong><br />
                  <pre className="mb-0">{powerActionResult.details}</pre>
                </div>
              )}
            </div>
          )}
        </div>
      </>,
      footer: <>
        <button
          className="btn btn-secondary"
          onClick={() => setModalShow(false)}
        >
          Close
        </button>
        <button
          className="btn btn-danger ms-2"
          onClick={() => executeServerPowerAction('off')}
          disabled={powerActionLoading}
        >
          {powerActionLoading ? (
            <>
              <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Processing...
            </>
          ) : (
            <>Shutdown</>
          )}
        </button>
      </>
    };


    const fetchTrafficData = useCallback(async (period = '24h') => {
      if (!server || !server.id) return;

      setLoadingTraffic(true);
      try {
        const token = sessionStorage.getItem('token');
        const response = await fetch(`/api.php?f=switch_port_traffic&server_id=${server.id}&period=${period}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ token })
        });

        const data = await response.json();

        if (data.error) {
          console.error('Error fetching traffic data:', data.error);
          // Fallback to zeros
          setTrafficData(createEmptyTrafficData());
        } else {
          setTrafficData(data);
          calculateNetworkStats(data);
        }
      } catch (error) {
        console.error('Error fetching traffic data:', error);
        // Fallback to zeros
        setTrafficData(createEmptyTrafficData());
      } finally {
        setLoadingTraffic(false);
      }
    }, [server]);

    // Helper to create empty data with zeros
    const createEmptyTrafficData = () => {
      const hours = ['00:00', '02:00', '04:00', '06:00', '08:00', '10:00',
                    '12:00', '14:00', '16:00', '18:00', '20:00', '22:00'];
      return hours.map(hour => ({
        time: hour,
        inbound: 0,
        outbound: 0,
        in_packets: 0,
        out_packets: 0
      }));
    };

    // Calculate network statistics based on traffic data
    const calculateNetworkStats = (data) => {
      if (!data || data.length === 0) {
        setNetworkStats({
          peakTraffic: 0,
          avgTraffic: 0,
          monthlyUsage: 0,
          packetsPerSec: 0
        });
        return;
      }

      // Calculate peak traffic (highest inbound + outbound)
      let peakTraffic = 0;
      let totalTraffic = 0;
      let totalPackets = 0;

      data.forEach(point => {
        const total = point.inbound + point.outbound;
        if (total > peakTraffic) {
          peakTraffic = total;
        }
        totalTraffic += total;
        totalPackets += (point.in_packets + point.out_packets);
      });

      // Average traffic in Gbps
      const avgTraffic = parseFloat((totalTraffic / data.length).toFixed(1));

      // Monthly usage in TB (approximate)
      // Convert Gbps to TB per month: Gbps * 60*60*24*30 seconds / 8 bits per byte / 1024^4 bytes per TB
      const monthlyUsage = parseFloat((avgTraffic * 60*60*24*30 / 8 / Math.pow(1024, 4) * 1000).toFixed(1));

      // Average packets per second
      const avgPacketsPerSec = data.length > 0 ? Math.round(totalPackets / data.length) : 0;

      setNetworkStats({
        peakTraffic: parseFloat(peakTraffic.toFixed(1)),
        avgTraffic,
        monthlyUsage,
        packetsPerSec: avgPacketsPerSec
      });
    };

    // Handle period change from dropdown
    const handlePeriodChange = (e) => {
      const newPeriod = e.target.value;
      setTrafficPeriod(newPeriod);
      fetchTrafficData(newPeriod);
    };

    // Add useEffect to load data when component mounts or server changes
    useEffect(() => {
      if (server && server.id && activeTab === 'network') {
        fetchTrafficData(trafficPeriod);
      }
    }, [server, activeTab, fetchTrafficData]);

    const reinstallModal = {
      title: <>Reinstall Operating System</>,
      body: <>
        <div className="form-group">
          <label className="form-label mb-2">Select Operating System:</label>
          <select name="os" className="form-control custom-select mb-3">
            <option value="ubuntu2204">Ubuntu Server 22.04 LTS</option>
            <option value="ubuntu2004">Ubuntu Server 20.04 LTS</option>
            <option value="debian11">Debian 11</option>
            <option value="centos9">CentOS Stream 9</option>
            <option value="windows2022">Windows Server 2022</option>
          </select>

          <div className="alert alert-info mb-3">
            <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
            After initiating the reinstallation procedure, the remote power control and reinstallation functions are disabled to avoid interrupting the process. You will receive an email and a notification in the client area when the reinstallation is complete.
          </div>

          <div className="alert alert-danger mb-0">
            <i className="fa fa-exclamation-triangle me-2" style={{paddingRight: '0.5rem'}}></i>
            <strong>WARNING:</strong> This function will delete all existing data from your server and leads to 10-20 min downtime.
          </div>
        </div>
      </>,
      footer: <><button className="btn btn-danger" onClick={() => setModalShow(false)}>Reinstall</button></>
    };

    // This section has been moved to the top of the component



    // This section has been removed as it's no longer needed



    // Define openModal function after all its dependencies are defined
    const openModal = useCallback((title, body, footer) => {
      // Reset power action state when opening a new modal
      setPowerActionLoading(false);
      setPowerActionResult(null);

      // Special case for the IP Configuration modal
      if (title && title.type === React.Fragment && title.props.children === "IP Configuration") {
        // const ipModal = createIpConfigModal(); // Old logic
        // setModalTitle(ipModal.title);
        // setModalBody(ipModal.body);
        // setModalFooter(ipModal.footer);
        // Instead of showing the generic IP modal, show the SubnetUpgradeModal
        setShowSubnetModal(true); // Modified to show SubnetUpgradeModal
        return; // Skip the rest of the function
      }
      // Special case for Bandwidth modal - use our new component
      else if (title && title.type === React.Fragment && title.props.children === "Change Bandwidth Plan") {
        // Instead of showing the modal directly, set our state to show the bandwidth modal
        setShowBandwidthModal(true);
        return; // Skip the rest of the function
      }
      else {
        // Default case - handle normally
        setModalTitle(title);
        setModalBody(body);
        setModalFooter(footer);
      }

      setModalShow(true);
    }, [createIpConfigModal, setShowBandwidthModal, setShowSubnetModal]);

    // Removed the old renewModal definition as we're now using the RenewServerModal component

    const getFirstUsableIp = (subnet) => {
      if (!subnet) return null;

      try {
        const subnetStr = String(subnet).trim();
        console.log("Parsing subnet:", subnetStr);

        // If subnet is just a CIDR (like "/29"), we can't calculate without a base IP
        if (subnetStr.startsWith('/')) {
          console.log("Subnet is just CIDR notation without base IP");
          return null;
        }

        let baseIp;
        let cidr = 29; // Default

        if (subnetStr.includes('/')) {
          const parts = subnetStr.split('/');
          baseIp = parts[0];
          cidr = parseInt(parts[1]);
        } else {
          const ipMatch = subnetStr.match(/^(\d+\.\d+\.\d+\.\d+)$/);
          if (ipMatch) {
            baseIp = ipMatch[1];
          } else {
            console.error("Invalid subnet format:", subnet);
            return null;
          }
        }

        const ipParts = baseIp.split('.');
        if (ipParts.length !== 4) {
          console.error("Invalid IP format:", baseIp);
          return null;
        }

        const ipOctets = ipParts.map(part => parseInt(part));
        const newParts = [...ipOctets];

        // Calculate first usable IP based on CIDR
        if (cidr === 31) {
          // /31: First usable IP is x.x.x.1
          newParts[3] = newParts[3] + 1;
        } else if (cidr === 30) {
          // /30: First usable IP is x.x.x.2
          newParts[3] = newParts[3] + 2;
        } else {
          // All others: First usable IP is x.x.x.2
          newParts[3] = newParts[3] + 2;
        }

        const result = newParts.join('.');
        console.log(`Calculated IP: ${result}`);
        return result;
      } catch (error) {
        console.error("Error calculating first usable IP:", error);
        return null;
      }
    };

    // Function to parse IPs from subnet
    const parseIpsFromSubnet = (subnet, mainIp) => {
      if (!subnet) return [];

      // Get the first usable IP from the subnet
      let firstUsableIp = getFirstUsableIp(subnet);

      // Make sure we're not including CIDR notation
      if (firstUsableIp && firstUsableIp.includes('/')) {
        firstUsableIp = firstUsableIp.split('/')[0];
      }

      // Similarly, ensure main_ip doesn't have CIDR notation
      let cleanMainIp = mainIp;
      if (cleanMainIp && cleanMainIp.includes('/')) {
        cleanMainIp = cleanMainIp.split('/')[0];
      }

      // Use the first usable IP or fall back to main_ip if available
      const baseIp = firstUsableIp || cleanMainIp;

      if (baseIp && baseIp !== 'N/A') {
        console.log("Using IP for generation:", baseIp);

        // Parse the IP
        const ipParts = baseIp.split('.');
        if (ipParts.length !== 4) {
          console.error("Invalid IP format:", baseIp);
          return [baseIp]; // Return just the IP if format is invalid
        }

        const baseOctet = parseInt(ipParts[3]);

        // Generate 5 sequential IPs starting from the base IP
        return Array.from({length: 5}, (_, i) => {
          const newParts = [...ipParts]; // Create a copy to avoid modifying the original
          newParts[3] = (baseOctet + i).toString();
          return newParts.join('.');
        });
      } else {
        console.log("No valid IP available for generation");
        return [];
      }
    };

    if (loading) {
      return (
        <div className="d-flex justify-content-center align-items-center" style={{ height: "300px" }}>
          <div className="spinner-border text-primary" role="status">

          </div>
        </div>
      );
    }

    if (!server) {
      return (
        <div className="alert alert-danger" role="alert">
          Server not found. <Link to="/">Return to services</Link>
        </div>
      );
    }

    // Extract IPs from subnet for display in Network tab
    console.log("Server main_ip:", server.main_ip);
    console.log("Server subnet:", server.subnet);
    const publicIps = parseIpsFromSubnet(server.subnet, server.main_ip);
    console.log("Generated publicIps:", publicIps);

    return (
      <>
              {/* Storage Upgrade Modal */}
  <StorageUpgradeModal
    isOpen={showStorageUpgradeModal}
    onClose={toggleStorageUpgradeModal}
    server={server}
    onSuccess={handleStorageUpgradeSuccess}
  />
        <div className="page-header">
          <div className="page-leftheader">
            <ol className="breadcrumb">
              <li className="breadcrumb-item1"><Link to="/">Services</Link></li>
              <li className="breadcrumb-item1">Server {server.label}</li>
            </ol>
          </div>
          <div className="page-rightheader ml-auto">
            <div className="dropdown">
              <a href="#" className="nav-link pr-0 leading-none" data-toggle="dropdown">
                <button className="btn btn-success">New Service</button>
              </a>
              <div className="dropdown-menu">
                <Link className="dropdown-item d-flex" to="/dedicatedorder">
                  <div className="mt-1">Dedicated Server</div>
                </Link>
                <Link className="dropdown-item d-flex" to="/cloudorder">
                  <div className="mt-1">Cloud Server</div>
                </Link>

              </div>
            </div>
          </div>
        </div>

        <div className="row">
          <div className="col-12">
            <DiscountAlert />

            <div className="card smaller">
              <div className="card-header">
                <div className="card-title d-flex align-items-center">
                  <span className="me-2">Server {server.label}</span>
                  {server.status === 'Active' && <span className="badge bg-success">Online</span>}
                  {server.status === 'Pending' && <span className="badge bg-warning">Pending</span>}
                  {server.status === 'Unpaid' && <span className="badge bg-danger">Unpaid</span>}
                  {server.status === 'Suspended' && <span className="badge bg-danger">Suspended</span>}
                  {server.status === 'terminated' && <span className="badge bg-danger">Terminated</span>}
                  {server.status === 'pending_termination' && <span className="badge bg-warning">Pending Termination</span>}
                </div>
                <div className="card-options">
                  {server.status !== 'terminated' ? (
                    <>
                      <button className="btn btn-success btn-sm me-2" onClick={() => openModal(reinstallModal.title, reinstallModal.body, reinstallModal.footer)}>
                        <i className="fa fa-cogs me-1"></i> Reinstall
                      </button>
                      <button className="btn btn-warning btn-sm me-2" onClick={() => openModal(rebootModal.title, rebootModal.body, rebootModal.footer)}>
                        <i className="fa fa-refresh me-1"></i> Reboot
                      </button>
                      <button className="btn btn-danger btn-sm" onClick={() => openModal(shutdownModal.title, shutdownModal.body, shutdownModal.footer)}>
                        <i className="fa fa-power-off me-1"></i> Shutdown
                      </button>
                    </>
                  ) : (
                    <span className="badge bg-danger">Server Terminated</span>
                  )}
                </div>
              </div>
              <div className="card-body">
                <div className="panel panel-primary">
                  <div className="tab-menu-heading">
                    <div className="tabs-menu">
                      <ul className="nav panel-tabs">
                        <li>
                          <a
                            className={activeTab === 'overview' ? 'active' : ''}
                            onClick={() => setActiveTab('overview')}
                            style={{cursor: 'pointer'}}
                          >
                            <i className="si si-screen-desktop me-1"></i> Overview
                          </a>
                        </li>
                        <li>
                          <a
                            className={activeTab === 'network' ? 'active' : ''}
                            onClick={() => setActiveTab('network')}
                            style={{cursor: 'pointer'}}
                          >
                            <i className="si si-graph me-1"></i> Network
                          </a>
                        </li>
                        <li>
                          <a
                            className={activeTab === 'settings' ? 'active' : ''}
                            onClick={() => setActiveTab('settings')}
                            style={{cursor: 'pointer'}}
                          >
                            <i className="si si-settings me-1"></i> Settings
                          </a>
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div className="panel-body tabs-menu-body">
                    <div className="tab-content">
                      {/* Overview Tab */}
                      {activeTab === 'overview' && (
                        <div className="tab-pane active">
                          <div className="row">
                            {/* Server Access and Quick Actions Card */}
                            <div className="col-xl-4 col-lg-6 col-md-12 mb-4">
                              <div className="card shadow-sm h-100">
                                <div className="card-header bg-light">
                                  <h3 className="card-title"><i className="fa fa-server me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Server Access</h3>
                                </div>
                                <div className="card-body">


                                  <h5 className="text-muted mb-3" style={{paddingRight: '0.5rem'}}>Quick Actions</h5>
                                  {server.status === 'terminated' ? (
                                    <div className="alert alert-danger">
                                      <i className="fa fa-exclamation-triangle me-2"></i>
                                      This server has been terminated and is no longer accessible.
                                    </div>
                                  ) : (
                                    <div className="row">
                                      <div className="col-6 mb-3">
                                        <button className="btn btn-light btn-block rounded-3 w-100 h-100 py-3 shadow-sm"
                                          onClick={() => openModal(startModal.title, startModal.body, startModal.footer)}>
                                          <i className="fa fa-power-off fa-2x d-block mb-2 text-success"></i>
                                          Turn On
                                        </button>
                                      </div>
                                      <div className="col-6 mb-3">
                                        <button
                                          className="btn btn-light btn-block rounded-3 w-100 h-100 py-3 shadow-sm"
                                          onClick={() => {
                                            if (server?.ipmi_ip) { // Changed from ipmi to ipmi_ip
                                              // Create a temporary element to hold our component
                                              const tempDiv = document.createElement('div');
                                              document.body.appendChild(tempDiv);

                                              // Render the IdracAutoDetectConsole component with the server data
                                              const idracRoot = ReactDOM.createRoot(tempDiv);
                                              idracRoot.render(
                                                <IdracAutoDetectConsole
                                                  ipmiAddress={server.ipmi_ip}
                                                  username={server.ipmi_user || 'root'}
                                                  password={server.ipmi_pass}
                                                  selectedItem={server}
                                                />
                                              );

                                              // After a slight delay, find the launch button and click it programmatically
                                              setTimeout(() => {
                                                const launchButton = tempDiv.querySelector('button');
                                                if (launchButton) {
                                                  launchButton.click();
                                                }

                                                // Clean up
                                                setTimeout(() => {
                                                  idracRoot.unmount();
                                                  document.body.removeChild(tempDiv);
                                                }, 1000);
                                              }, 100);
                                            }
                                          }}
                                          disabled={!server?.ipmi_ip} // Changed from ipmi to ipmi_ip
                                        >
                                          <i className="fa fa-desktop fa-2x d-block mb-2 text-primary"></i>
                                          Console
                                        </button>
                                      </div>
                                    </div>
                                  )}

                                  <h5 className="text-muted mb-3">Hardware</h5>
                                  <ul className="list-group list-group-flush">
                                    <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                      <span><i className="fa fa-microchip text-primary me-2"></i> CPU:</span>
                                      <span className="text-muted">{server.cpu}</span>
                                    </li>
                                    <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                      <span><i className="fa fa-cogs text-primary me-2"></i> RAM:</span>
                                      <span className="text-muted">{server.ram}</span>
                                    </li>
                                    <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                      <span><i className="fa fa-hdd-o text-primary me-2"></i> Storage:</span>
                                      <span className="text-muted">{server.disks}</span>
                                    </li>
                                    <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                      <span><i className="fa fa-wifi text-primary me-2"></i> Bandwidth:</span>
                                      <span className="text-muted">{server.bandwidth}</span>
                                    </li>
                                  </ul>
                                </div>
                                <button 
    className="btn btn-primary w-100"
    onClick={toggleStorageUpgradeModal}
  >
    <i className="fa fa-arrow-up me-1"></i> Storage Upgrade
  </button>
                              </div>
                            </div>

                            {/* Status and Activity Card */}
                            <div className="col-xl-4 col-lg-6 col-md-12 mb-4">
                              <div className="card shadow-sm h-100">
                                <div className="card-header bg-light">
                                  <h3 className="card-title"><i className="fa fa-info-circle me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Status & Activity</h3>
                                </div>
                                <div className="card-body">
                                  <h5 className="text-muted mb-3">Server Status</h5>
                                  <ul className="list-group list-group-flush mb-4">
                                    <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-plug text-primary me-2"></i> Power Status:</span>
                                      {powerStatusLoading ? (
                                        <div className="d-flex align-items-center">
                                          <div className="spinner-border spinner-border-sm me-2" role="status" style={{ width: '1rem', height: '1rem' }}>

                                          </div>
                                          <span>Checking...</span>
                                        </div>
                                      ) : (
                                        <>
                                          {powerStatus === 'on' && <span className="badge bg-success">Online</span>}
                                          {powerStatus === 'off' && <span className="badge bg-danger">Offline</span>}
                                          {powerStatus === 'Unknown' && <span className="badge bg-secondary">{server.server_status || 'Unknown'}</span>}
                                        </>
                                      )}
                                    </li>
                                    <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-plug text-primary me-2"></i> Port Status:</span>
                                      <span className="text-muted">{server.switchport}</span>
                                    </li>

                                  </ul>

                                  <h5 className="text-muted mb-3">Location</h5>
                                  <ul className="list-group list-group-flush mb-4">
                                    <li className="list-group-item d-flex justify-content-between align-items-center px-0" >
                                      <span><i className="fa fa-building text-primary me-2"></i> Data Center:</span>
                                      <span className="text-muted">
                                        <i className={`flag flag-${server.location}`} style={{marginRight: '8px'}}></i>
                                        {server.locationname}
                                      </span>
                                    </li>
                                    <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                      <span><i className="fa fa-map text-primary me-2"></i> Facility:</span>
                                      <span className="text-muted">{server.datacenter}</span>
                                    </li>
      
                                  </ul>


                                  <div className="mb-4">
                                    <h5 className="text-muted mb-3">Operating System</h5>
                                    <div className="card">
                                      <div className="card-body p-4">
                                        <div className="row align-items-center">
                                          <div className="col-auto">
                                            {/* Use a simple div with background image instead of img tag */}
                                            <div
                                              style={{
                                                width: "40px",
                                                height: "40px",
                                                backgroundImage: `url('${server.os_logo_url}')`,
                                                backgroundSize: "contain",
                                                backgroundPosition: "center",
                                                backgroundRepeat: "no-repeat"
                                              }}
                                            ></div>
                                          </div>
                                          <div className="col">
                                            <h6 className="mb-1">{server.os}</h6>
                                            <div className="text-muted">Linux</div>
                                          </div>
                                          <div className="col-auto">
                                            <button
                                              className="btn btn-primary"
                                              onClick={() => openModal(reinstallModal.title, reinstallModal.body, reinstallModal.footer)}
                                            >
                                              Change
                                            </button>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>


                                  </div>
                                  </div>
                            </div>

                            {/* Billing Card */}
                            <div className="col-xl-4 col-lg-12 col-md-12 mb-4">
                              <div className="card shadow-sm h-100">
                                <div className="card-header bg-light">
                                  <h3 className="card-title"><i className="fa fa-credit-card me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Details & Billing</h3>
                                </div>
                                <div className="card-body">


                                <h5 className="text-muted mb-3">Access Details</h5>
                                <ul className="list-group list-group-flush mb-4">


                                <li className="list-group-item d-flex justify-content-between align-items-center px-0">
    <span><i className="fa fa-globe text-primary me-2"></i> IP Address:</span>
    <div className="d-flex align-items-center">
      {(() => {
        // First try to use the complete subnet if available
        let displayIp = null;

        // If subnet doesn't contain an IP, use main_ip with the subnet CIDR
        if (server.subnet && server.main_ip && server.subnet.startsWith('/')) {
          // Get just the IP part from main_ip
          const baseIp = server.main_ip.split('/')[0];
          // Combine with the CIDR from subnet
          const fullSubnet = baseIp + server.subnet;
          console.log("Constructed subnet:", fullSubnet);
          displayIp = getFirstUsableIp(fullSubnet);
        } else if (server.subnet) {
          displayIp = getFirstUsableIp(server.subnet);
        } else if (server.main_ip) {
          displayIp = getFirstUsableIp(server.main_ip);
        }

        // If calculation fails, show 'No IP available'
        if (!displayIp) {
          displayIp = 'No IP available';
        }

        return (
          <>
            <span className="me-2 text-start" style={{paddingRight: '0.5rem'}}>{displayIp}</span>
            <button
              className="btn btn-sm btn-light"
              onClick={() => copyToClipboard(displayIp)}
              title="Copy to clipboard"
            >
              <i className="fa fa-copy"></i>
            </button>
          </>
        );
      })()}
    </div>
  </li>
    <li className="list-group-item d-flex justify-content-between align-items-center px-0">
      <span><i className="fa fa-user text-primary me-2"></i> Username:</span>
      <div className="d-flex align-items-center">
        <span className="me-2 text-start">{server.ipmi_user} </span>
        <button
          className="btn btn-sm btn-light" style={{marginLeft:'1rem'}}
          onClick={() => copyToClipboard(server.ipmi_user)}
          title="Copy to clipboard"
        >
          <i className="fa fa-copy"></i>
        </button>
      </div>
    </li>
    <li className="list-group-item d-flex justify-content-between align-items-center px-0">
      <span><i className="fa fa-key text-primary me-2"></i> Password:</span>
      <div className="d-flex align-items-center">
        <span className="me-2 text-start">
          {showPassword ? (server.password || '••••••••••••') : '••••••••••••'}
        </span>
        <button
          className="btn btn-sm btn-light me-2" style={{marginLeft:'1rem'}}
          onClick={togglePassword}
        >
          <i className={`fa fa-${showPassword ? 'eye-slash' : 'eye'}`}></i>
        </button>
        <button
          className="btn btn-sm btn-light" style={{marginLeft:'0.5rem'}}
          onClick={() => copyToClipboard(server.password)}
          title="Copy to clipboard"
        >
          <i className="fa fa-copy"></i>
        </button>
      </div>
    </li>
  </ul>



                                  <h5 className="text-muted mb-3">Plan Details</h5>
                                  <ul className="list-group list-group-flush mb-4">
                                    <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                      <span><i className="fa fa-money text-primary me-2"></i> Monthly Price:</span>
                                      <span className="text-muted fw-bold">€{server.monthly_price || server.price}</span>
                                    </li>
                                    <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                      <span><i className="fa fa-refresh text-primary me-2"></i> Billing Cycle:</span>
                                      <span className="text-muted">
                                        {getBillingCycleName(server.payment_period)}
                                        {/* Debug info */}
                                        {console.log("Payment period in render:", server.payment_period)}
                                      </span>
                                    </li>
                                    <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                      <span><i className="fa fa-calendar text-primary me-2"></i> Next Renewal:</span>
                                      <span className="text-muted">{server.expires}</span>
                                    </li>
                                    <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                      <span><i className="fa fa-repeat text-primary me-2"></i> Auto-Renewal:</span>
                                      <div className="d-flex align-items-center">
                                        {isUpdatingAutoRenewal && (
                                          <div className="spinner-border spinner-border-sm me-2" role="status" style={{ width: '1rem', height: '1rem' }}>

                                          </div>
                                        )}
                                        <span
                                          className={`badge ${server.auto_renewal ? 'bg-success' : 'bg-danger'}`}
                                          style={{ cursor: isUpdatingAutoRenewal ? 'wait' : 'pointer' }}
                                          onClick={() => {
                                            if (isUpdatingAutoRenewal) return; // Prevent clicking while updating

                                            // Get the new state (opposite of current)
                                            const newAutoRenewalState = !server.auto_renewal;

                                            // Immediately update the UI to show the change
                                            setServer({...server, auto_renewal: newAutoRenewalState});

                                            // Set updating state to true to show loading
                                            setIsUpdatingAutoRenewal(true);

                                            console.log(`Auto-renewal changing to: ${newAutoRenewalState ? 'enabled' : 'disabled'}`);

                                            // Call the API to update auto-renewal status
                                            (async () => {
                                              try {
                                              const token = sessionStorage.getItem('token');
                                              const response = await fetch('/api.php?f=update_auto_renewal', {
                                                method: 'POST',
                                                headers: {
                                                  'Content-Type': 'application/json'
                                                },
                                                body: JSON.stringify({
                                                  token,
                                                  server_id: server.id,
                                                  auto_renewal: newAutoRenewalState
                                                })
                                              });

                                              const data = await response.json();
                                              console.log("Auto-renewal update response:", data);

                                              if (data.success) {
                                                // Update status message (could be shown as a toast notification)
                                                console.log(`Auto-renewal ${newAutoRenewalState ? 'enabled' : 'disabled'} successfully`);
                                              } else {
                                                // Show error message
                                                console.error('Error updating auto-renewal status:', data.error || data.message);

                                                // Revert checkbox state
                                                setServer({...server, auto_renewal: !newAutoRenewalState});

                                                // Show error message
                                                alert('Error updating auto-renewal status: ' + (data.error || data.message || 'Unknown error'));
                                              }
                                            } catch (error) {
                                              console.error('Error updating auto-renewal status:', error);

                                              // Revert checkbox state
                                              setServer({...server, auto_renewal: !newAutoRenewalState});

                                              // Show error message
                                              alert('Error updating auto-renewal status: ' + error.message);
                                            } finally {
                                              // Re-enable the badge
                                              setIsUpdatingAutoRenewal(false);
                                            }
                                            })();
                                          }}
                                        >
                                          {server.auto_renewal ? 'Enabled' : 'Disabled'}
                                        </span>
                                      </div>
                                    </li>
                                  </ul>

                                  <div className="mt-4 d-grid gap-2">

                                  <div className="mt-4 d-flex justify-content-between">
                                  {server.status !== 'terminated' ? (
                                    <button className="btn btn-danger btn-sm me-2" onClick={() => openModal(terminateModal.title, terminateModal.body, terminateModal.footer)}>
                                      <i className="fa fa-times-circle me-1"></i> Terminate
                                    </button>
                                  ) : (
                                    <span className="badge bg-danger">Server Terminated</span>
                                  )}

                                    <div>
  <Link
    to={`/billing/invoices`}
    className="btn btn-info btn-sm"
    onClick={() => {
      // Scroll to top when the button is clicked
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }}
  >
    <i className="fa fa-arrow-circle-left me-1"></i> View Invoices
  </Link>
                                      <button className="btn btn-success btn-sm" onClick={() => setShowRenewModal(true)}>
                                        <i className="fa fa-refresh me-1"></i> Renew Service
                                      </button>
                                      </div>
                                  </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Network Tab */}
                      {activeTab === 'network' && (
                        <div className="tab-pane active">
                          <div className="row">
<div className="col-xl-4 col-lg-6 col-md-12 mb-4">
  <div className="card shadow-sm">
    <div className="card-header bg-light">
      <h3 className="card-title"><i className="fa fa-globe me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Network Configuration</h3>
    </div>
    <div className="card-body">
      <ul className="list-group list-group-flush">
        <li className="list-group-item d-flex justify-content-between align-items-center px-0">
          <span><i className="fa fa-sitemap"></i> Current Subnet:</span>
          <span>{server.subnet || 'N/A'}</span>
        </li>
        <li className="list-group-item d-flex justify-content-between align-items-center px-0">
          <span><i className="fa fa-tag"></i> Ordered Subnet:</span>
          <span>{server.ordered_subnet || 'N/A'}</span>
        </li>
        <li className="list-group-item d-flex justify-content-between align-items-center px-0">
          <span><i className="fa fa-wifi"></i> Bandwidth:</span>
          <span>{server.bandwidth}</span>
        </li>
      </ul>

      <div className="mt-4 d-grid">
        <button
          className="btn btn-primary"
          onClick={() => setShowBandwidthModal(true)}
        >
          <i className="fa fa-arrow-up me-1"></i> Upgrade Bandwidth Plan
        </button>
      </div>
    </div>
  </div>
</div>


                            <div className="col-xl-8 col-lg-6 col-md-12 mb-4">
    <div className="card shadow-sm">
      <div className="card-header bg-light">
        <h3 className="card-title"><i className="fa fa-globe me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>IP Addresses</h3>
        <div className="card-options">
          <button className="btn btn-sm btn-primary" onClick={() => openModal(
            <>IP Configuration</>, null, null
          )}>
            <i className="fa fa-plus me-1"></i> Upgrade Subnet
          </button>
        </div>
      </div>
      <div className="card-body">
      <div className="table-responsive" style={{ maxHeight: '500px', overflowY: 'auto' }}>
    <table className="table table-hover" style={{ position: 'relative' }}>
      <thead className="bg-light" style={{ position: 'sticky', top: 0, zIndex: 10 }}>
        <tr>
          <th>IP Address</th>
          <th>Type</th>
          <th>Subnet</th>
        </tr>
      </thead>
      <tbody>
        {(() => {
          const ipInfos = getAllUsableIpsFromSubnet(server);
          console.log(`Rendering ${ipInfos.length} IP addresses`);
          return ipInfos.map((ipInfo, index) => (
            <tr key={index}>
              <td>
                <div className="d-flex align-items-center">
                  <span className="me-2" style={{paddingRight: '0.5rem'}}>{ipInfo.ip}</span>
                  {ipInfo.isMain && <span className="badge bg-primary ms-1">Main IP</span>}
                </div>
              </td>
              <td>{ipInfo.isMain ? 'Primary' : 'Secondary'}</td>
              <td>{ipInfo.source}</td>
        

            </tr>
          ));
        })()}
      </tbody>
    </table>
  </div>
      </div>
    </div>
  </div>

  <div className="col-12">
    <div className="card shadow-sm">
      <div className="card-header bg-light">
        <h3 className="card-title"><i className="fa fa-globe me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Network Traffic</h3>
        <div className="card-options">
          <select
            className="form-control form-control-sm"
            style={{ minHeight: "35px" }}
            value={trafficPeriod}
            onChange={handlePeriodChange}
            disabled={loadingTraffic}
          >
            <option value="24h">Last 24 hours</option>
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
          </select>
        </div>
      </div>
      <div className="card-body">
        {loadingTraffic ? (
          <div className="d-flex justify-content-center align-items-center" style={{ height: '300px' }}>
            <div className="spinner-border text-primary" role="status">

            </div>
          </div>
        ) : (
          <div style={{ width: '100%', height: 300 }}>
            <ResponsiveContainer>
              <LineChart
                data={trafficData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" />
                <YAxis />
                <Tooltip formatter={(value) => [`${value} Gbps`]} />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="inbound"
                  stroke="#3498db"
                  activeDot={{ r: 8 }}
                  name="Inbound"
                  strokeWidth={2}
                />
                <Line
                  type="monotone"
                  dataKey="outbound"
                  stroke="#e74c3c"
                  name="Outbound"
                  strokeWidth={2}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        )}
        <div className="mt-4">
          <h5 className="text-muted mb-3">Network Statistics</h5>
          <div className="row">
            <div className="col-md-3 col-6">
              <div className="card bg-light p-3 text-center h-100">
                <h6 className="text-muted mb-2">Peak Traffic</h6>
                <h3 className="mb-0">{networkStats.peakTraffic} Gbps</h3>
              </div>
            </div>
            <div className="col-md-3 col-6">
              <div className="card bg-light p-3 text-center h-100">
                <h6 className="text-muted mb-2">Average Traffic</h6>
                <h3 className="mb-0">{networkStats.avgTraffic} Gbps</h3>
              </div>
            </div>
            <div className="col-md-3 col-6">
              <div className="card bg-light p-3 text-center h-100">
                <h6 className="text-muted mb-2">Monthly Usage</h6>
                <h3 className="mb-0">{networkStats.monthlyUsage} TB</h3>
              </div>
            </div>
            <div className="col-md-3 col-6">
              <div className="card bg-light p-3 text-center h-100">
                <h6 className="text-muted mb-2">Packets/sec</h6>
                <h3 className="mb-0">{networkStats.packetsPerSec.toLocaleString()}</h3>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
                          </div>
                        </div>
                      )}

                      {/* Settings Tab */}
                      {activeTab === 'settings' && (
                        <div className="tab-pane active">
                          <div className="row">
                            <div className="col-md-6 mb-4">
                              <div className="card shadow-sm">
                                <div className="card-header bg-light">
                                  <h3 className="card-title"><i className="fa fa-cog me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Server Settings</h3>
                                </div>
                                <div className="card-body">
  <form onSubmit={handleLabelUpdate}>
    <div className="form-group mb-3">
      <label className="form-label">Server Label</label>
      <input
        type="text"
        name="serverLabel"
        className={`form-control ${labelFormError ? 'is-invalid' : ''}`}
        defaultValue={server.label}
        placeholder="Server Label"
        required
      />
      {labelFormError && (
        <div className="invalid-feedback">{labelFormError}</div>
      )}
      <small className="form-text text-muted">
        This label identifies your server in the control panel.
      </small>
    </div>



    <div className="d-flex justify-content-end">
      <button
        type="submit"
        className="btn btn-primary"
        disabled={loading}
      >
        {loading ? (
          <>
            <span className="spinner-border spinner-border-sm me-2" role="status"></span>
            Saving...
          </>
        ) : 'Save Settings'}
      </button>
    </div>
  </form>
  </div>
  </div>
  </div>

                            <div className="col-md-6 mb-4">
                              <div className="card shadow-sm">
                                <div className="card-header bg-light">

                                  <h3 className="card-title"><i className="fa fa-credit-card me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Billing Settings</h3>
                                </div>
                                <div className="card-body">
                                  <form>
            

                      

                                    <div className="form-group mb-3">

    <div className="d-flex align-items-center mb-2">
    <span
        className={`badge ${server.auto_renewal ? 'bg-success' : 'bg-danger'}`}
        style={{
          cursor: isUpdatingAutoRenewal ? 'wait' : 'pointer',
          padding: '8px 12px',
          fontSize: '14px'
        }}
        onClick={() => {
          if (isUpdatingAutoRenewal) return; // Prevent clicking while updating

          // Get the new state (opposite of current)
          const newAutoRenewalState = !server.auto_renewal;

          // Immediately update the UI to show the change
          setServer({...server, auto_renewal: newAutoRenewalState});

          // Set updating state to true to show loading
          setIsUpdatingAutoRenewal(true);

          // Set updating state to show the spinner

          console.log(`Auto-renewal changing to: ${newAutoRenewalState ? 'enabled' : 'disabled'}`);

          // Call the API to update auto-renewal status
          (async () => {
            try {
              const token = sessionStorage.getItem('token');
              const response = await fetch('/api.php?f=update_auto_renewal', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  token,
                  server_id: server.id,
                  auto_renewal: newAutoRenewalState
                })
              });

              const data = await response.json();
              console.log("Auto-renewal update response:", data);

              if (data.success) {
                // Success - no message needed
                console.log(`Auto-renewal ${newAutoRenewalState ? 'enabled' : 'disabled'} successfully`);
              } else {
                // Show error message
                console.error('Error updating auto-renewal status:', data.error || data.message);

                // Revert checkbox state
                setServer({...server, auto_renewal: !newAutoRenewalState});
              }
            } catch (error) {
              console.error('Error updating auto-renewal status:', error);

              // Revert badge state
              setServer({...server, auto_renewal: !newAutoRenewalState});
            } finally {
              // Re-enable the badge
              setIsUpdatingAutoRenewal(false);
            }
          })();
        }}
      >
        {server.auto_renewal ? 'Enabled' : 'Disabled'}
      </span>
    <label className="form-label">Auto-Renewal</label>

      {isUpdatingAutoRenewal && (
        <div className="spinner-border spinner-border-sm ms-2" role="status" style={{ width: '1rem', height: '1rem' }}>
        </div>
      )}
    </div>
    <small className="form-text text-muted d-block">
      When disabled, your server will be suspended after the billing period ends unless manually renewed.
      Click the badge to toggle auto-renewal.
    </small>
  </div>

  <div className="form-group mb-3">
    <div className="d-flex align-items-center mb-2">
    <span
        className={`badge ${server.use_credit ? 'bg-success' : 'bg-danger'}`}
        style={{
          cursor: isUpdatingUseCredit ? 'wait' : 'pointer',
          padding: '8px 12px',
          fontSize: '14px'
        }}
        onClick={() => {
          if (isUpdatingUseCredit) return; // Prevent clicking while updating

          // Get the new state (opposite of current)
          const newUseCreditState = !server.use_credit;

          // Immediately update the UI to show the change
          setServer({...server, use_credit: newUseCreditState});

          // Set updating state to true to show loading
          setIsUpdatingUseCredit(true);

          console.log(`Use credit changing to: ${newUseCreditState ? 'enabled' : 'disabled'}`);

          // Call the API to update use_credit status
          (async () => {
            try {
              const token = sessionStorage.getItem('token');
              const response = await fetch('/api.php?f=update_use_credit', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  token,
                  server_id: server.id,
                  use_credit: newUseCreditState
                })
              });

              const data = await response.json();
              console.log("Use credit update response:", data);

              if (data.success) {
                // Success - no message needed
                console.log(`Use credit ${newUseCreditState ? 'enabled' : 'disabled'} successfully`);
              } else {
                // Show error message
                console.error('Error updating use credit status:', data.error || data.message);

                // Revert checkbox state
                setServer({...server, use_credit: !newUseCreditState});
              }
            } catch (error) {
              console.error('Error updating use credit status:', error);

              // Revert badge state
              setServer({...server, use_credit: !newUseCreditState});
            } finally {
              // Re-enable the badge
              setIsUpdatingUseCredit(false);
            }
          })();
        }}
      >
        {server.use_credit ? 'Enabled' : 'Disabled'}
      </span>
      <label className="form-label">Use Account Credit for Renewals</label>

      {isUpdatingUseCredit && (
        <div className="spinner-border spinner-border-sm ms-2" role="status" style={{ width: '1rem', height: '1rem' }}>
        </div>
      )}
    </div>
    <small className="form-text text-muted d-block">
      When enabled, available account credit will be automatically applied to renewal invoices.
      Click the badge to toggle this setting.
    </small>
  </div>

  </form>
  </div>
  </div>



                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Modal for various actions */}

        <MDBModal show={modalShow} setShow={setModalShow} tabIndex='-1'>
          <MDBModalDialog>
            <MDBModalContent>
              <MDBModalHeader>
                <MDBModalTitle>{modalTitle}</MDBModalTitle>
                <button className="close" onClick={() => setModalShow(false)}><span aria-hidden="true">×</span></button>
              </MDBModalHeader>
              <MDBModalBody>
                {modalBody}
              </MDBModalBody>
              <MDBModalFooter>
                {modalFooter}
              </MDBModalFooter>
            </MDBModalContent>
          </MDBModalDialog>
        </MDBModal>

        {/* New Bandwidth Upgrade Modal */}
        <BandwidthUpgradeModal
          isOpen={showBandwidthModal}
          onClose={() => setShowBandwidthModal(false)}
          server={server}
          onSuccess={handleBandwidthUpgradeSuccess}
        />

        {/* New Subnet Upgrade Modal */}
        <SubnetUpgradeModal
          isOpen={showSubnetModal}
          onClose={() => setShowSubnetModal(false)}
          server={server}
          onSuccess={handleSubnetUpgradeSuccess}
        />

        {/* New Server Renewal Modal */}
        <RenewServerModal
          isOpen={showRenewModal}
          onClose={() => setShowRenewModal(false)}
          server={server}
          onSuccess={handleRenewalSuccess}
        />
      </>
    );
  };

  export default ServerDetailsView;