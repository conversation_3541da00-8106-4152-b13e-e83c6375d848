import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  MDBModal,
  MDBModalDialog,
  MDBModalContent,
  MDBModalHeader,
  MDBModalTitle,
  MDBModalBody,
  MDBModalFooter,
} from 'mdb-react-ui-kit';

/**
 * SubnetUpgradeModal Component
 *
 * A reusable modal component for upgrading server subnet plans.
 *
 * @param {Object} props - Component props
 * @param {boolean} props.isOpen - Controls whether the modal is displayed
 * @param {Function} props.onClose - Function to call when the modal is closed
 * @param {Object} props.server - Server data object
 * @param {Function} props.onSuccess - Function to call after successful subnet upgrade
 */
const SubnetUpgradeModal = ({ isOpen, onClose, server, onSuccess }) => {
  // State for subnet plans and selection
  const [subnetPlans, setSubnetPlans] = useState([]);
  const [selectedSubnetId, setSelectedSubnetId] = useState(null);
  const [currentSubnetId, setCurrentSubnetId] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [debugInfo, setDebugInfo] = useState({});

  // VAT-related state - ADDED FROM STORAGE MODAL
  const [vatInfo, setVatInfo] = useState({
    vat_rate: 0,
    vat_exempt: false,
    country: null,
    vat_id: null
  });
  const [isLoadingVat, setIsLoadingVat] = useState(false);

  // Fetch VAT information - ADDED FROM STORAGE MODAL
  const fetchVatInfo = useCallback(async () => {
    setIsLoadingVat(true);
    try {
      const token = sessionStorage.getItem('token');
      const response = await fetch('/api.php?f=get_vat_rate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      const responseText = await response.text();
      console.log("Raw VAT rate response:", responseText);

      let data;
      try {
        data = JSON.parse(responseText);
        console.log("Parsed VAT data:", data);
      } catch (e) {
        console.error("Failed to parse VAT response:", e);
        return;
      }

      if (data && data.success) {
        setVatInfo({
          vat_rate: parseFloat(data.vat_rate) || 0,
          vat_exempt: data.vat_exempt || false,
          country: data.country || null,
          vat_id: data.vat_id || null
        });
      }
    } catch (error) {
      console.error("Error fetching VAT info:", error);
    } finally {
      setIsLoadingVat(false);
    }
  }, []);

  // Helper function to extract CIDR from subnet name
  const extractCIDR = (subnetName) => {
    if (!subnetName) return null;
    const match = subnetName.match(/\/(\d+)/);
    return match ? parseInt(match[1], 10) : null;
  };

  // Helper function to get usable IPs count from CIDR
  const getUsableIPs = (cidr) => {
    if (cidr === null) return 0;
    if (cidr >= 32) return 1;
    return Math.max(0, Math.pow(2, 32 - cidr) - 2);
  };

  // Helper function to determine if one subnet is an upgrade from another
  const isSubnetUpgrade = (currentPlan, targetPlan) => {
    if (!currentPlan || !targetPlan) return false;
    
    // Special handling for custom subnets
    if (currentPlan.name.includes("My own") || targetPlan.name.includes("My own")) {
      // Custom subnets can be considered upgrades if they have higher price or are different
      return targetPlan.price >= currentPlan.price && targetPlan.id !== currentPlan.id;
    }
    
    const currentCIDR = extractCIDR(currentPlan.name);
    const targetCIDR = extractCIDR(targetPlan.name);
    
    if (currentCIDR === null || targetCIDR === null) {
      // Fallback to price comparison for non-standard subnets
      return targetPlan.price > currentPlan.price;
    }
    
    // Lower CIDR number = larger subnet = upgrade
    // Also check price to ensure it's not a downgrade in terms of cost
    return targetCIDR < currentCIDR || (targetCIDR === currentCIDR && targetPlan.price > currentPlan.price);
  };

  // Calculate prorated amount for the upgrade
  const calculateProratedAmount = useCallback(() => {
    // Initial checks for selected plan and plan data
    if (!selectedSubnetId || !subnetPlans || !subnetPlans.length) {
      return 0;
    }

    const selectedId = parseInt(selectedSubnetId, 10);
    const selectedPlan = subnetPlans.find(plan => parseInt(plan.id, 10) === selectedId);

    if (!selectedPlan) {
      console.log(`Selected subnet plan not found - selectedId: ${selectedSubnetId}`);
      return 0;
    }

    const newPrice = parseFloat(selectedPlan.price || 0);
    let currentPrice = 0; // Default to 0 for assignments or if current plan can't be determined

    // If there is a current subnet, try to find its details and price
    if (currentSubnetId) {
      if (selectedSubnetId === currentSubnetId) { // If selected is the same as current
        return 0;
      }
      const currentId = parseInt(currentSubnetId, 10);
      const currentPlan = subnetPlans.find(plan => parseInt(plan.id, 10) === currentId);

      if (currentPlan) {
        currentPrice = parseFloat(currentPlan.price || 0);
      } else {
        // currentSubnetId was provided, but no matching plan found.
        console.warn(`Current subnet plan (ID: ${currentSubnetId}) not found in subnetPlans. Assuming €0 current price for proration calculation.`);
        // For calculation purposes, currentPrice remains 0. If this scenario implies an error,
        // the invoice generation logic might catch it or need adjustment.
      }
    }

    // Calculate price difference (newPrice for assignments, difference for upgrades)
    const priceDifference = newPrice - currentPrice;

    // Only proceed if there's a positive cost difference (for upgrades) or positive cost (for assignments)
    if (priceDifference <= 0) {
      return 0;
    }

    // Proration logic
    const today = new Date();
    const dueDate = server?.expires ? new Date(server.expires) : null;

    if (!dueDate) {
      // If no due date, charge full difference (which is newPrice for assignment)
      return Math.round(priceDifference * 100) / 100;
    }

    const daysRemaining = Math.max(0, Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24)));

    // If no days remaining (due date is today or in the past), prorated amount should be 0.
    // Unless business logic dictates charging the full priceDifference if it's the last day.
    // Current logic: (priceDifference / 30) * 0 = 0. This seems reasonable.
    const proratedAmount = (priceDifference / 30) * daysRemaining; // Standard 30-day month assumption

    return Math.round(proratedAmount * 100) / 100;
  }, [selectedSubnetId, currentSubnetId, subnetPlans, server]);

  // Fetch subnet plans from API
  const fetchSubnetPlans = useCallback(async () => {
    if (!server || !server.id) return;

    setIsLoading(true);
    setError('');

    try {
      const token = sessionStorage.getItem('token');
      const response = await fetch('/api.php?f=get_subnet_plans', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      const responseText = await response.text();
      console.log("Raw subnet plans response:", responseText);

      let data;
      try {
        data = JSON.parse(responseText);
        console.log("Parsed subnet plans:", data);
      } catch (e) {
        console.error("Failed to parse subnet plans response:", e);
        setError('Invalid response from server for subnet plans');
        return;
      }

      if (data && Array.isArray(data)) {
        // Convert all IDs to numbers for consistency
        const plansWithNumericIds = data.map(plan => ({
          ...plan,
          id: parseInt(plan.id, 10),
          price: parseFloat(plan.price || 0)
        }));

        // Sort plans by CIDR (descending) then by price for logical ordering
        plansWithNumericIds.sort((a, b) => {
          const cidrA = extractCIDR(a.name);
          const cidrB = extractCIDR(b.name);
          
          // Put custom subnets (no CIDR) at the end
          if (cidrA === null && cidrB === null) return a.price - b.price;
          if (cidrA === null) return 1;
          if (cidrB === null) return -1;
          
          // Sort by CIDR (descending - smaller subnets first)
          if (cidrA !== cidrB) return cidrB - cidrA;
          
          // If same CIDR, sort by price
          return a.price - b.price;
        });
        
        setSubnetPlans(plansWithNumericIds);

        // DEBUG: Log all server properties for debugging
        console.log("=== SUBNET DETECTION DEBUG ===");
        console.log("Full server object:", server);
        console.log("server.subnet_id:", server.subnet_id, typeof server.subnet_id);
        console.log("server.subnet:", server.subnet);
        console.log("server.current_subnet_name:", server.current_subnet_name);
        console.log("Available subnet plans:", plansWithNumericIds.map(p => ({ id: p.id, name: p.name, price: p.price })));

        // Determine the current subnet ID - ENHANCED LOGIC
        let currentId = null;
        let detectionMethod = "none";

        // Method 1: Direct subnet_id (most reliable)
        if (server && (server.subnet_id !== null && server.subnet_id !== undefined)) {
          const rawSubnetId = server.subnet_id;
          currentId = parseInt(rawSubnetId, 10);
          
          console.log("Method 1 - Raw subnet_id:", rawSubnetId, "Parsed:", currentId);
          
          // Verify this ID exists in the plans
          const matchingPlan = plansWithNumericIds.find(plan => plan.id === currentId);
          if (matchingPlan && !isNaN(currentId)) {
            console.log("✅ Method 1 SUCCESS - Found matching plan:", matchingPlan.name);
            detectionMethod = "subnet_id";
          } else {
            console.warn("❌ Method 1 FAILED - subnet_id doesn't match any available plans or is invalid");
            currentId = null;
          }
        } else {
          console.log("Method 1 SKIPPED - No subnet_id field or it's null/undefined");
        }

        // Method 2: Parse subnet field (fallback)
        if (currentId === null && server && server.subnet) {
          console.log("Method 2 - Parsing subnet field:", server.subnet);

          // Try to match by subnet name (e.g., "/29 (5 usable IPs)")
          const subnetMatch = server.subnet.match(/\/(\d+)/);
          if (subnetMatch && subnetMatch[1]) {
            const cidr = parseInt(subnetMatch[1], 10);
            console.log("Extracted CIDR from subnet:", cidr);

            // Find the plan with the matching CIDR in the name
            const matchingPlan = plansWithNumericIds.find(plan => {
              return plan.name && plan.name.includes(`/${cidr}`);
            });

            if (matchingPlan) {
              currentId = matchingPlan.id;
              console.log("✅ Method 2 SUCCESS - Found matching plan by CIDR:", matchingPlan.name);
              detectionMethod = "subnet_parsing";
            } else {
              console.warn("❌ Method 2 FAILED - No plan found for CIDR:", cidr);
            }
          } else {
            console.log("Method 2 FAILED - Could not extract CIDR from subnet field");
          }
        } else {
          console.log("Method 2 SKIPPED - No subnet field available");
        }

        // Method 3: Exact subnet name match (additional fallback)
        if (currentId === null && server && server.current_subnet_name) {
          console.log("Method 3 - Matching by current_subnet_name:", server.current_subnet_name);
          
          const matchingPlan = plansWithNumericIds.find(plan =>
            plan.name === server.current_subnet_name
          );

          if (matchingPlan) {
            currentId = matchingPlan.id;
            console.log("✅ Method 3 SUCCESS - Found matching plan by name:", matchingPlan.name);
            detectionMethod = "subnet_name";
          } else {
            console.warn("❌ Method 3 FAILED - No exact name match found");
          }
        } else {
          console.log("Method 3 SKIPPED - No current_subnet_name field");
        }

        // Final result
        if (currentId === null) {
          console.log("❌ ALL METHODS FAILED - Could not determine current subnet");
          console.log("This will be treated as a new subnet assignment");
        }

        // Set the current and selected subnet IDs
        setCurrentSubnetId(currentId);
        setSelectedSubnetId(currentId);
        
        console.log("=== FINAL RESULTS ===");
        console.log("Detection method used:", detectionMethod);
        console.log("Final current subnet ID:", currentId);
        console.log("Selected subnet ID:", currentId);
        if (currentId) {
          const finalPlan = plansWithNumericIds.find(p => p.id === currentId);
          console.log("Current subnet plan:", finalPlan ? finalPlan.name : "NOT FOUND");
        }
        console.log("=== END DEBUG ===");
        
      } else {
        setError('No subnet plans available');
      }
    } catch (error) {
      console.error("Error fetching subnet plans:", error);
      setError('Failed to load subnet plans. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [server]);

  // Handle subnet change submission
  const handleSubnetChange = async () => {
    setError('');
    setDebugInfo({});

    if (!selectedSubnetId) {
      setError('Please select a subnet plan');
      return;
    }

    const currentId = currentSubnetId ? parseInt(currentSubnetId, 10) : null;
    const selectedId = parseInt(selectedSubnetId, 10);

    if (currentId === selectedId) {
      setError('Please select a different subnet plan');
      return;
    }

    if (!subnetPlans || !subnetPlans.length) {
      setError('Subnet plan information is unavailable');
      return;
    }

    const currentPlan = currentId ? subnetPlans.find(plan => parseInt(plan.id, 10) === currentId) : null;
    const selectedPlan = subnetPlans.find(plan => parseInt(plan.id, 10) === selectedId);

    if (!selectedPlan) {
      console.error("Selected plan not found - Selected ID:", selectedId);
      const debugData = {
        currentId: currentId,
        selectedId: selectedId,
        subnetPlans: subnetPlans.map(p => ({ id: p.id, name: p.name })),
        server: { id: server?.id, subnet_id: server?.subnet_id }
      };
      setDebugInfo(debugData);
      setError('Invalid subnet plan selection. Technical information has been logged.');
      return;
    }

    // FIXED upgrade validation logic using subnet hierarchy
    if (currentPlan && !isSubnetUpgrade(currentPlan, selectedPlan)) {
      const currentCIDR = extractCIDR(currentPlan.name);
      const selectedCIDR = extractCIDR(selectedPlan.name);
      
      if (currentCIDR !== null && selectedCIDR !== null) {
        setError(`You can only upgrade to a larger subnet. Current: ${currentPlan.name}, Selected: ${selectedPlan.name}`);
      } else {
        setError('You can only upgrade to a higher-tier subnet plan.');
      }
      return;
    }

    try {
      setIsSubmitting(true);
      setDebugInfo({
        stage: 'starting_request',
        currentPlan,
        selectedPlan
      });

      const token = sessionStorage.getItem('token');

      const generateSubnetUpgradeInvoice = async () => {
        try {
          console.log("Using direct invoice generation for subnet upgrade");
          setDebugInfo(prev => ({ ...prev, stage: 'direct_subnet_invoice_generation' }));

          const proratedAmount = calculateProratedAmount();
          setDebugInfo(prev => ({ ...prev, proratedAmount }));

          const serverLabel = server.label || server.hostname || `Server ${server.id}`;
          const description = currentPlan
            ? `Subnet upgrade for server #${server.id} (${serverLabel}) from ${currentPlan.name} to ${selectedPlan.name}`
            : `Subnet assignment of ${selectedPlan.name} for ${serverLabel} (ID: ${server.id})`;

          // UPDATED VAT CALCULATION - Using fetched VAT info instead of server VAT rate
          const vatRate = vatInfo.vat_exempt ? 0 : (vatInfo.vat_rate || 0);
          const vatAmount = proratedAmount * (vatRate / 100);

          // Use the standard invoice generation endpoint with proper metadata
          const invoicePayload = {
            token: token,
            amount: proratedAmount,
            order_id: server.order_id || server.id,
            type: "Subnet Upgrade",
            description: description,
            vat_rate: vatRate,
            vat_amount: vatAmount,
            server_expires: server.expires,
            metadata: JSON.stringify({
              action: 'subnet_upgrade',
              server_id: server.id,
              current_subnet_id: currentId,
              new_subnet_id: selectedId,
              server_expires: server.expires,
              hostname: server.hostname || server.server_hostname || server.label || null,
              server_label: server.label || null,
              main_ip: server.main_ip || null,
              location: server.locationname || server.datacenter || null
            })
          };

          console.log("Subnet invoice generation payload:", invoicePayload);
          setDebugInfo(prev => ({ ...prev, invoicePayload }));

          // Use the standard generate_invoice endpoint like bandwidth upgrades do
          const response = await fetch('/api.php?f=generate_invoice', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(invoicePayload)
          });

          const responseText = await response.text();
          console.log("Subnet invoice generation response:", responseText);
          setDebugInfo(prev => ({ ...prev, rawResponse: responseText }));

          const data = JSON.parse(responseText);
          setDebugInfo(prev => ({ ...prev, parsedResponse: data }));

          if (data.success) {
            const enhancedResponse = {
              ...data,
              current_plan: currentPlan,
              new_plan: selectedPlan,
              prorated_amount: proratedAmount,
              vat_rate: vatRate,
              vat_amount: data.details?.tax || vatAmount,
              total_amount: data.details?.total || (proratedAmount + vatAmount),
              message: data.message || `Subnet ${currentPlan ? 'upgrade' : 'assignment'} invoice #${data.invoice_number} generated for ${selectedPlan.name}.`
            };
            console.log("Enhanced success response for subnet:", enhancedResponse);
            return enhancedResponse;
          }
          return data;
        } catch (error) {
          console.error("Error generating subnet upgrade invoice:", error);
          setDebugInfo(prev => ({
            ...prev,
            error: error.message,
            errorStack: error.stack
          }));
          return {
            success: false,
            error: "Error generating subnet upgrade invoice: " + error.message
          };
        }
      };

      if (!server || !server.id) {
        setError('Server information or Server ID is missing');
        setIsSubmitting(false);
        return;
      }

      const data = await generateSubnetUpgradeInvoice();

      if (!data) {
        console.error("No data returned from subnet API");
        setError('No response from server for subnet upgrade');
        setIsSubmitting(false);
        return;
      }

      console.log("Processing subnet API response:", data);
      setDebugInfo(prev => ({ ...prev, stage: 'processing_subnet_response', finalResponse: data }));

      if (data.success === true) {
        console.log("Subnet upgrade invoice generated successfully");
        const formattedData = {
          ...data,
          message: data.message || "Subnet upgrade request submitted. An invoice has been generated. Your subnet will be upgraded/assigned after payment."
        };
        if (typeof onSuccess === 'function') {
          onSuccess(formattedData);
        }
        onClose();
      } else {
        console.error("Subnet API returned error:", data.error || "Unknown error");
        
        // Enhanced error handling
        if (data.error && data.error.includes("unpaid")) {
          setError(data.error);
        } else if (data.error && data.error.includes("Invalid")) {
          setError(
            "Failed to generate invoice. " +
            "Please contact support and mention server ID: " + server.id
          );
        } else if (data.error && data.error.includes("permission")) {
          setError(
            "You don't have permission to generate an invoice for this server. " +
            "Please contact support for assistance."
          );
        } else if (data.error && data.error.includes("not found")) {
          setError(
            "The server information could not be found. " +
            "Please contact support and mention server ID: " + server.id
          );
        } else {
          setError(data.error || 'Failed to generate subnet upgrade invoice');
        }
      }
    } catch (error) {
      console.error("Error changing subnet:", error);
      setError('Connection error - please try again');
      setDebugInfo(prev => ({
        ...prev,
        fatalError: error.message,
        fatalErrorStack: error.stack
      }));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Load subnet plans and VAT info when the modal opens - UPDATED
  useEffect(() => {
    if (isOpen && server) {
      console.log("SubnetUpgradeModal opened with server data:", server);
      console.log("Server ID for subnet modal:", server.id);

      // Log all relevant subnet fields for debugging
      console.log("Server subnet_id:", server.subnet_id);
      console.log("Server subnet:", server.subnet);
      console.log("Server current_subnet_name:", server.current_subnet_name);

      setError('');
      setDebugInfo({});
      setIsLoading(true);

      // Fetch both subnet plans and VAT info
      fetchSubnetPlans();
      fetchVatInfo();
    }
  }, [isOpen, server, fetchSubnetPlans, fetchVatInfo]);

  // Reset state when modal closes - UPDATED
  useEffect(() => {
    if (!isOpen) {
      setError('');
      setSelectedSubnetId(null);
      setCurrentSubnetId(null);
      setDebugInfo({});
      // Reset VAT info
      setVatInfo({
        vat_rate: 0,
        vat_exempt: false,
        country: null,
        vat_id: null
      });
    }
  }, [isOpen]);

  return (
    <MDBModal show={isOpen} tabIndex='-1'>
      <MDBModalDialog>
        <MDBModalContent>
          <MDBModalHeader>
            <MDBModalTitle>Subnet Plan Upgrade/Assignment</MDBModalTitle>
            <button className="close" onClick={onClose}><span aria-hidden="true">×</span></button>
          </MDBModalHeader>

          <MDBModalBody>
            {/* UPDATED loading state to include VAT loading */}
            {(isLoading || isLoadingVat) ? (
              <div className="text-center py-4">
                <div className="spinner-border text-primary mb-3" role="status"></div>
                <p>Loading subnet plans and tax information...</p>
              </div>
            ) : (
              <div className="form-group">

                <label className="form-label mb-1">Select New Subnet Plan:</label>
                <select
                  name="subnet"
                  className="form-control custom-select mb-2"
                  value={selectedSubnetId || ''}
                  onChange={(e) => {
                    const newSubnetId = parseInt(e.target.value, 10);
                    if (!isNaN(newSubnetId)) {
                      setSelectedSubnetId(newSubnetId);
                      setError('');
                    }
                  }}
                  disabled={isSubmitting}
                >
                  <option value="" disabled>Select a subnet plan</option>
                  {subnetPlans.map(plan => {
                    const planId = parseInt(plan?.id || 0, 10);
                    const currentId = currentSubnetId ? parseInt(currentSubnetId, 10) : null;

                    const currentPlanDetails = currentId ? subnetPlans.find(p => parseInt(p.id, 10) === currentId) : null;

                    let isUpgradeOption = true;
                    let optionText = plan?.name || `Plan ${planId}`;

                    if (currentPlanDetails) {
                      // Use the new upgrade logic
                      isUpgradeOption = isSubnetUpgrade(currentPlanDetails, plan);
                      
                      if (planId === currentId) {
                        optionText += ' (Current)';
                      } else if (isUpgradeOption) {
                        const priceDiff = parseFloat(plan?.price || 0) - parseFloat(currentPlanDetails?.price || 0);
                        if (priceDiff > 0) {
                          optionText += ` (+€${priceDiff.toFixed(2)}/mo)`;
                        } else if (priceDiff === 0) {
                          optionText += ' (Upgrade)';
                        }
                      } else {
                        optionText += ' (Not an upgrade)';
                      }
                    } else {
                      // No current plan, it's an assignment
                      optionText += ` (€${parseFloat(plan?.price || 0).toFixed(2)}/mo)`;
                    }

                    return (
                      <option
                        key={planId || `subnet-plan-${Math.random()}`}
                        value={planId}
                        disabled={planId === currentId || (currentPlanDetails && !isUpgradeOption)}
                      >
                        {optionText}
                      </option>
                    );
                  })}
                </select>


                {error && (
                  <div className="alert alert-danger mb-2">
                    <i className="fa fa-exclamation-circle me-1" style={{paddingRight: '0.5rem'}}></i>
                    {error}
                  </div>
                )}

                {selectedSubnetId && selectedSubnetId !== currentSubnetId && (
                  <div className="card mb-2">
                    <div className="card-header bg-light py-1"><h5 className="mb-0">Price Summary</h5></div>
                    <div className="card-body py-2">
                      {(() => {
                        const currentPlan = currentSubnetId ? subnetPlans.find(p => parseInt(p.id, 10) === parseInt(currentSubnetId, 10)) : null;
                        const selectedPlan = subnetPlans.find(p => parseInt(p.id, 10) === parseInt(selectedSubnetId, 10));

                        if (!selectedPlan) {
                          return <p>Unable to calculate price difference.</p>;
                        }

                        // Component prices (subnet only)
                        const currentSubnetPrice = currentPlan ? parseFloat(currentPlan.price || 0) : 0;
                        const newSubnetPrice = parseFloat(selectedPlan.price || 0);
                        
                        // Get base server price (total current price minus current subnet price)
                        const currentTotalPrice = parseFloat(server.monthly_price || server.price || 0);
                        const baseServerPrice = Math.max(0, currentTotalPrice - currentSubnetPrice);
                        
                        // Calculate new total service price
                        const newTotalPrice = baseServerPrice + newSubnetPrice;
                        const totalPriceDifference = newTotalPrice - currentTotalPrice;

                        const proratedAmount = calculateProratedAmount();
                        
                        // UPDATED VAT calculation using fetched VAT info
                        const vatRate = vatInfo.vat_exempt ? 0 : (vatInfo.vat_rate || 0);
                        const vatAmount = proratedAmount * (vatRate / 100);

                        return (
                          <>
                            <div className="row mb-3">
                              <div className="col-md-6">
                                <h6 className="text-muted mb-2">Current Service</h6>
                                <div className="card">
                                  <div className="card-body p-2">
                                    <div><strong>Total Monthly Cost</strong></div>
                                    <div className="small text-muted mb-2">
                                      <div>Base Server: €{baseServerPrice.toFixed(2)}</div>
                                      {currentPlan && <div>Subnet ({currentPlan.name}): €{currentSubnetPrice.toFixed(2)}</div>}
                                      {!currentPlan && <div>Subnet: None assigned</div>}
                                    </div>
                                    <div className="h6 mb-0 text-primary">€{currentTotalPrice.toFixed(2)}/month</div>
                                  </div>
                                </div>
                              </div>
                              <div className="col-md-6">
                                <h6 className="text-muted mb-2">After {currentPlan ? 'Upgrade' : 'Assignment'}</h6>
                                <div className="card border-success">
                                  <div className="card-body p-2">
                                    <div><strong>Total Monthly Cost</strong></div>
                                    <div className="small text-muted mb-2">
                                      <div>Base Server: €{baseServerPrice.toFixed(2)}</div>
                                      <div>Subnet ({selectedPlan.name}): €{newSubnetPrice.toFixed(2)}</div>
                                    </div>
                                    <div className="h6 mb-0 text-success">€{newTotalPrice.toFixed(2)}/month</div>
                                  </div>
                                </div>
                              </div>
                            </div>
                            
                            <hr className="my-2" />
                            
                            <div className="d-flex justify-content-between mb-1">
                              <div>Monthly Price {currentPlan ? 'Increase' : 'Addition'}:</div>
                              <div>€{totalPriceDifference.toFixed(2)}</div>
                            </div>
                            <div className="d-flex justify-content-between mb-1">
                              <div>Prorated Amount:</div>
                              <div>€{proratedAmount.toFixed(2)}</div>
                            </div>
                            {/* UPDATED VAT display using fetched VAT info */}
                            {!vatInfo.vat_exempt && vatRate > 0 && (
                              <div className="d-flex justify-content-between mb-1">
                                <div>VAT ({vatRate.toFixed(1)}%{vatInfo.country ? ` - ${vatInfo.country}` : ''}):</div>
                                <div>€{vatAmount.toFixed(2)}</div>
                              </div>
                            )}
                            {vatInfo.vat_exempt && (
                              <div className="d-flex justify-content-between mb-1">
                                <div>VAT:</div>
                                <div>Exempt</div>
                              </div>
                            )}
                            <div className="d-flex justify-content-between mb-1">
                              <div><strong>Total Amount Due Now:</strong></div>
                              <div><strong>€{(proratedAmount + vatAmount).toFixed(2)}</strong></div>
                            </div>
                            <div className="small text-muted">
                              Prorated to next billing date ({server?.expires || 'N/A'}).
                              New monthly cost will be €{newTotalPrice.toFixed(2)} starting from next billing cycle.
                              {vatInfo.country && ` VAT based on ${vatInfo.country}.`}
                              {vatInfo.vat_exempt && ' VAT exempt status applied.'}
                            </div>
                          </>
                        );
                      })()}
                    </div>
                  </div>
                )}

                <div className="alert alert-info mb-2 py-2">
                  <i className="fa fa-info-circle me-1" style={{paddingRight: '0.5rem'}}></i>
                  <strong>Billing Process:</strong> Clicking "Generate Invoice" will create an invoice for the prorated amount.
                  Your subnet will be assigned/upgraded after the invoice is paid.
                </div>

                <div className="alert alert-warning mb-2 py-2">
                  <i className="fa fa-exclamation-triangle me-1" style={{paddingRight: '0.5rem'}}></i>
                  <strong>Note:</strong> When upgrading to a new subnet plan, the current IP addresses will be changed.
                </div>
              </div>
            )}
          </MDBModalBody>

          <MDBModalFooter>
            <button
              className="btn btn-secondary"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              className="btn btn-success ms-2"
              onClick={handleSubnetChange}
              disabled={
                isLoading ||
                isLoadingVat ||  // ADDED VAT loading check
                isSubmitting ||
                !selectedSubnetId ||
                (currentSubnetId && selectedSubnetId === currentSubnetId) ||
                !subnetPlans ||
                subnetPlans.length === 0
              }
            >
              {isSubmitting ? (
                <>
                  <span className="spinner-border spinner-border-sm me-1" role="status"></span>
                  Processing...
                </>
              ) : (
                <>Generate Invoice</>
              )}
            </button>
          </MDBModalFooter>
        </MDBModalContent>
      </MDBModalDialog>
    </MDBModal>
  );
};

export default SubnetUpgradeModal;