import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  MDBModal,
  MDBModalDialog,
  MDBModalContent,
  MDBModalHeader,
  MDBModalTitle,
  MDBModalBody,
  MDBModalFooter,
} from 'mdb-react-ui-kit';

/**
 * BandwidthUpgradeModal Component
 *
 * A reusable modal component for upgrading server bandwidth plans.
 * Now includes CPU-based bandwidth restrictions from dedicated_configurations table.
 *
 * @param {Object} props - Component props
 * @param {boolean} props.isOpen - Controls whether the modal is displayed
 * @param {Function} props.onClose - Function to call when the modal is closed
 * @param {Object} props.server - Server data object (must include cpu_id)
 * @param {Function} props.onSuccess - Function to call after successful bandwidth upgrade
 */
const BandwidthUpgradeModal = ({ isOpen, onClose, server, onSuccess }) => {
  // State for bandwidth plans and selection
  const [bandwidthPlans, setBandwidthPlans] = useState([]);
  const [selectedBandwidthId, setSelectedBandwidthId] = useState(null);
  const [currentBandwidthId, setCurrentBandwidthId] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [debugInfo, setDebugInfo] = useState({});
  const [cpuRestrictionInfo, setCpuRestrictionInfo] = useState(null);
  
  // VAT-related state
  const [vatInfo, setVatInfo] = useState({
    vat_rate: 0,
    vat_exempt: false,
    country: null,
    vat_id: null
  });
  const [isLoadingVat, setIsLoadingVat] = useState(false);

  // Fetch VAT information
  const fetchVatInfo = useCallback(async () => {
    setIsLoadingVat(true);
    try {
      const token = sessionStorage.getItem('token');
      const response = await fetch('/api.php?f=get_vat_rate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      const responseText = await response.text();
      console.log("Raw VAT rate response:", responseText);

      let data;
      try {
        data = JSON.parse(responseText);
        console.log("Parsed VAT data:", data);
      } catch (e) {
        console.error("Failed to parse VAT response:", e);
        return;
      }

      if (data && data.success) {
        setVatInfo({
          vat_rate: parseFloat(data.vat_rate) || 0,
          vat_exempt: data.vat_exempt || false,
          country: data.country || null,
          vat_id: data.vat_id || null
        });
      }
    } catch (error) {
      console.error("Error fetching VAT info:", error);
    } finally {
      setIsLoadingVat(false);
    }
  }, []);

  // Calculate prorated amount for the upgrade
  const calculateProratedAmount = useCallback(() => {
    if (!selectedBandwidthId || !currentBandwidthId ||
        selectedBandwidthId === currentBandwidthId ||
        !bandwidthPlans || !bandwidthPlans.length) {
      return 0;
    }

    const selectedId = parseInt(selectedBandwidthId, 10);
    const currentId = parseInt(currentBandwidthId, 10);
    
    const currentPlan = bandwidthPlans.find(plan => parseInt(plan.id, 10) === currentId);
    const selectedPlan = bandwidthPlans.find(plan => parseInt(plan.id, 10) === selectedId);

    if (!currentPlan || !selectedPlan) {
      console.log(`Plans not found - currentId: ${currentId}, selectedId: ${selectedId}`);
      return 0;
    }

    const currentPrice = parseFloat(currentPlan.price || 0);
    const newPrice = parseFloat(selectedPlan.price || 0);
    const priceDifference = newPrice - currentPrice;

    if (priceDifference <= 0) {
      return 0;
    }

    const today = new Date();
    const dueDate = server?.expires ? new Date(server.expires) : null;

    if (!dueDate) {
      return priceDifference;
    }

    const daysRemaining = Math.max(0, Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24)));
    const proratedAmount = (priceDifference / 30) * daysRemaining;

    return Math.round(proratedAmount * 100) / 100;
  }, [selectedBandwidthId, currentBandwidthId, bandwidthPlans, server]);

  // Fetch bandwidth plans from API with CPU restrictions
  const fetchBandwidthPlans = useCallback(async () => {
    if (!server || !server.id) return;

    setIsLoading(true);
    setError('');
    setCpuRestrictionInfo(null);
    
    try {
      const token = sessionStorage.getItem('token');
      
      // Include server CPU information in the request
      const payload = {
        token,
        server_id: server.id,
        cpu_id: server.cpu_id || null  // Include CPU ID if available
      };
      
      console.log("Fetching bandwidth plans with payload:", payload);
      
      const response = await fetch('/api.php?f=get_bandwidth_plans', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      const responseText = await response.text();
      console.log("Raw bandwidth plans response:", responseText);

      let data;
      try {
        data = JSON.parse(responseText);
        console.log("Parsed bandwidth plans:", data);
      } catch (e) {
        console.error("Failed to parse bandwidth plans response:", e);
        setError('Invalid response from server');
        return;
      }

      // Check if the response includes CPU restriction information
      if (data && data.cpu_restricted) {
        setCpuRestrictionInfo({
          cpu_id: data.cpu_id,
          cpu_name: data.cpu_name || `CPU ID ${data.cpu_id}`,
          allowed_bandwidths: data.allowed_bandwidths || [],
          total_available: data.total_available || 0,
          total_restricted: data.total_restricted || 0
        });
      }

      // Handle the bandwidth plans array
      let plans = [];
      if (data && data.plans && Array.isArray(data.plans)) {
        plans = data.plans;
      } else if (data && Array.isArray(data)) {
        plans = data;
      }

      if (plans.length > 0) {
        const plansWithNumericIds = plans.map(plan => ({
          ...plan,
          id: parseInt(plan.id, 10),
          speed: parseInt(plan.speed, 10),
          price: parseFloat(plan.price || 0),
          available_for_cpu: plan.available_for_cpu !== false  // Default to true if not specified
        }));

        // Sort plans by speed for easier comparison
        plansWithNumericIds.sort((a, b) => a.speed - b.speed);
        setBandwidthPlans(plansWithNumericIds);

        // Determine the current bandwidth ID
        let currentId = null;

        if (server && server.bandwidth_id) {
          currentId = parseInt(server.bandwidth_id, 10);
          console.log("Using bandwidth_id from server:", currentId);
        } else if (server && server.bandwidth) {
          console.log("Server has bandwidth field:", server.bandwidth);

          const bandwidthMatch = server.bandwidth.match(/(\d+)/);
          if (bandwidthMatch && bandwidthMatch[1]) {
            const bandwidthSpeed = parseInt(bandwidthMatch[1], 10);
            console.log("Extracted bandwidth speed:", bandwidthSpeed);

            const matchingPlan = plansWithNumericIds.find(plan => {
              const planSpeed = parseInt(plan.speed, 10) / 1000;
              return planSpeed === bandwidthSpeed;
            });

            if (matchingPlan) {
              currentId = matchingPlan.id;
              console.log("Found matching plan by speed:", currentId);
            }
          }
        }

        if (!currentId && plansWithNumericIds.length > 0) {
          currentId = plansWithNumericIds[0].id;
          console.log("Defaulting to first plan:", currentId);
        }

        setCurrentBandwidthId(currentId);
        setSelectedBandwidthId(currentId);
      } else {
        setError('No bandwidth plans available for this server configuration');
      }
    } catch (error) {
      console.error("Error fetching bandwidth plans:", error);
      setError('Failed to load bandwidth plans. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [server]);

  // Handle bandwidth change submission
  const handleBandwidthChange = async () => {
    setError('');
    setDebugInfo({});

    if (!selectedBandwidthId) {
      setError('Please select a bandwidth plan');
      return;
    }

    if (!currentBandwidthId) {
      setError('Current bandwidth information is missing');
      return;
    }

    const currentId = parseInt(currentBandwidthId, 10);
    const selectedId = parseInt(selectedBandwidthId, 10);

    if (currentId === selectedId) {
      setError('Please select a different bandwidth plan');
      return;
    }

    if (!bandwidthPlans || !bandwidthPlans.length) {
      setError('Bandwidth plan information is unavailable');
      return;
    }

    const currentPlan = bandwidthPlans.find(plan => parseInt(plan.id, 10) === currentId);
    const selectedPlan = bandwidthPlans.find(plan => parseInt(plan.id, 10) === selectedId);

    if (!currentPlan || !selectedPlan) {
      console.error("Plan not found - Current:", !currentPlan, "Selected:", !selectedPlan);
      setError('Invalid bandwidth plan selection');
      return;
    }

    // Check if selected plan is available for this CPU
    if (selectedPlan.available_for_cpu === false) {
      setError('This bandwidth plan is not available for your server configuration');
      return;
    }

    const currentSpeed = parseInt(currentPlan.speed, 10) || 0;
    const selectedSpeed = parseInt(selectedPlan.speed, 10) || 0;

    if (selectedSpeed <= currentSpeed) {
      setError('You can only upgrade to a higher bandwidth plan');
      return;
    }

    try {
      setIsSubmitting(true);

      const token = sessionStorage.getItem('token');

      const generateBandwidthUpgradeInvoice = async () => {
        try {
          console.log("Generating bandwidth upgrade invoice");

          const proratedAmount = calculateProratedAmount();
          
          const description = `Bandwidth upgrade from ${currentPlan.name} (${currentPlan.speed} Mbps) to ${selectedPlan.name} (${selectedPlan.speed} Mbps) for server ID ${server.id}`;
          
          const vatRate = vatInfo.vat_exempt ? 0 : (vatInfo.vat_rate || 0);
          const vatAmount = proratedAmount * (vatRate / 100);
          
          const invoicePayload = {
            token: sessionStorage.getItem('token'),
            amount: proratedAmount,
            order_id: server.order_id || server.id,
            type: "Bandwidth Upgrade",
            description: description,
            vat_rate: vatRate,
            vat_amount: vatAmount,
            server_expires: server.expires,
            metadata: JSON.stringify({
              action: 'bandwidth_upgrade',
              server_id: server.id,
              current_bandwidth_id: currentId,
              new_bandwidth_id: selectedId,
              server_expires: server.expires,
              hostname: server.hostname || server.server_hostname || server.label || null,
              server_label: server.label || null,
              main_ip: server.main_ip || null,
              location: server.locationname || server.datacenter || null,
              bandwidth: server.bandwidth || null,
              cpu: server.cpu || null,
              cpu_id: server.cpu_id || null  // Include CPU ID for reference
            })
          };
          
          console.log("Invoice generation payload:", invoicePayload);
          
          const response = await fetch('/api.php?f=generate_invoice', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(invoicePayload)
          });
          
          const responseText = await response.text();
          console.log("Invoice generation response:", responseText);
          
          const data = JSON.parse(responseText);
          
          if (data.success) {
            const enhancedResponse = {
              ...data,
              current_plan: currentPlan,
              new_plan: selectedPlan,
              prorated_amount: proratedAmount,
              vat_rate: vatRate,
              vat_amount: data.details?.tax || vatAmount,
              total_amount: data.details?.total || (proratedAmount + vatAmount),
              message: `Bandwidth upgrade invoice generated successfully. Invoice #${data.invoice_id} has been created for ${selectedPlan.name}.`
            };
            
            return enhancedResponse;
          }
          
          return data;
        } catch (error) {
          console.error("Error generating bandwidth upgrade invoice:", error);
          
          return {
            success: false,
            error: "Error generating bandwidth upgrade invoice: " + error.message
          };
        }
      };

      if (!server || !server.id) {
        setError('Server information is missing');
        return;
      }

      const data = await generateBandwidthUpgradeInvoice();

      if (!data) {
        console.error("No data returned from API");
        setError('No response from server');
        return;
      }

      console.log("Processing API response:", data);

      if (data.success === true) {
        console.log("Bandwidth upgrade invoice generated successfully");

        const formattedData = {
          ...data,
          message: data.message || "Bandwidth upgrade request submitted. An invoice has been generated. Your bandwidth will be upgraded after payment is processed."
        };

        if (typeof onSuccess === 'function') {
          console.log("Calling onSuccess callback with data:", formattedData);
          onSuccess(formattedData);
        }

        onClose();
      } else {
        console.error("API returned error:", data.error || "Unknown error");

        if (data.error && data.error.includes("Invalid")) {
          setError(
            "Failed to generate invoice. " +
            "Please contact support and mention server ID: " + server.id
          );
        } else if (data.error && data.error.includes("permission")) {
          setError(
            "You don't have permission to generate an invoice for this server. " +
            "Please contact support for assistance."
          );
        } else if (data.error && data.error.includes("not found")) {
          setError(
            "The server information could not be found. " +
            "Please contact support and mention server ID: " + server.id
          );
        } else {
          setError(data.error || 'Failed to generate bandwidth upgrade invoice');
        }
      }
    } catch (error) {
      console.error("Error changing bandwidth:", error);
      setError('Connection error - please try again');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Load bandwidth plans and VAT info when the modal opens
  useEffect(() => {
    if (isOpen && server) {
      console.log("Modal opened with server data:", server);
      console.log("Server ID:", server.id);
      console.log("Server CPU ID:", server.cpu_id);
      console.log("Server bandwidth_id:", server.bandwidth_id);
      console.log("Server bandwidth:", server.bandwidth);

      setError('');
      setDebugInfo({});
      setIsLoading(true);

      fetchBandwidthPlans();
      fetchVatInfo();
    }
  }, [isOpen, server, fetchBandwidthPlans, fetchVatInfo]);

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setError('');
      setSelectedBandwidthId(null);
      setDebugInfo({});
      setCpuRestrictionInfo(null);
      setVatInfo({
        vat_rate: 0,
        vat_exempt: false,
        country: null,
        vat_id: null
      });
    }
  }, [isOpen]);

  return (
    <MDBModal show={isOpen} tabIndex='-1'>
      <MDBModalDialog>
        <MDBModalContent>
          <MDBModalHeader>
            <MDBModalTitle>Bandwidth Plan Upgrade</MDBModalTitle>
            <button className="close" onClick={onClose}><span aria-hidden="true">×</span></button>
          </MDBModalHeader>

          <MDBModalBody>
            {(isLoading || isLoadingVat) ? (
              <div className="text-center py-4">
                <div className="spinner-border text-primary mb-3" role="status"></div>
                <p>Loading bandwidth plans and tax information...</p>
              </div>
            ) : (
              <div className="form-group">
                {cpuRestrictionInfo && (
                  <div className="alert alert-info mb-3">
                    <i className="fa fa-info-circle me-1" style={{paddingRight: '0.5rem'}}></i>
                    <strong>Server Configuration:</strong> Your server's CPU ({cpuRestrictionInfo.cpu_name}) 
                    supports {cpuRestrictionInfo.allowed_bandwidths.length} bandwidth plans out of {cpuRestrictionInfo.total_available} total options.
                  </div>
                )}
                
                <label className="form-label mb-1">Select New Bandwidth Plan:</label>
                <select
                  name="bandwidth"
                  className="form-control custom-select mb-2"
                  value={selectedBandwidthId || ''}
                  onChange={(e) => {
                    const newBandwidthId = parseInt(e.target.value, 10);
                    if (!isNaN(newBandwidthId)) {
                      setSelectedBandwidthId(newBandwidthId);
                      setError('');
                    }
                  }}
                  disabled={isSubmitting || bandwidthPlans.length === 0}
                >
                  <option value="" disabled>
                    {bandwidthPlans.length === 0 ? 'No bandwidth plans available' : 'Select a bandwidth plan'}
                  </option>
                  {bandwidthPlans.map(plan => {
                    const planId = parseInt(plan?.id || 0, 10);
                    const currentId = parseInt(currentBandwidthId || 0, 10);

                    const currentPlan = bandwidthPlans.find(p => {
                      const pId = parseInt(p?.id || 0, 10);
                      return pId === currentId;
                    });

                    let isUpgrade = false;
                    let isAvailable = plan.available_for_cpu !== false;

                    if (currentPlan && isAvailable) {
                      const currentSpeed = parseInt(currentPlan.speed || 0, 10);
                      const planSpeed = parseInt(plan?.speed || 0, 10);
                      isUpgrade = planSpeed > currentSpeed;
                    }

                    const isDisabled = planId === currentId || !isUpgrade || !isAvailable;

                    return (
<option
  key={planId}
  value={planId}
  disabled={isDisabled}
>
  {plan?.name || `Plan ${planId}`}
  {planId === currentId ? ' (Current)' :
    !isAvailable ? ' (Not available for your CPU)' :
    isUpgrade ? (() => {
      const currentPlanPrice = parseFloat(currentPlan?.price || 0);
      const newPlanPrice = parseFloat(plan?.price || 0);
      const priceDifference = newPlanPrice - currentPlanPrice;
      return ` (+€${priceDifference.toFixed(2)}/mo)`;
    })() :
    ' (Not an upgrade - unavailable)'}
</option>
                    );
                  })}
                </select>

                {bandwidthPlans.length === 0 && !isLoading && (
                  <div className="alert alert-warning mb-2">
                    <i className="fa fa-exclamation-triangle me-1" style={{paddingRight: '0.5rem'}}></i>
                    No bandwidth plans are available for your server configuration. Please contact support for assistance.
                  </div>
                )}

                {error && (
                  <div className="alert alert-danger mb-2">
                    <i className="fa fa-exclamation-circle me-1" style={{paddingRight: '0.5rem'}}></i>
                    {error}
                  </div>
                )}

                {selectedBandwidthId && selectedBandwidthId !== currentBandwidthId && (
                  <div className="card mb-2">
                    <div className="card-header bg-light py-1"><h5 className="mb-0">Price Summary</h5></div>
                    <div className="card-body py-2">
                      {(() => {
                        const currentPlan = bandwidthPlans.find(plan =>
                          parseInt(plan?.id || 0, 10) === parseInt(currentBandwidthId || 0, 10)
                        );
                        const selectedPlan = bandwidthPlans.find(plan =>
                          parseInt(plan?.id || 0, 10) === parseInt(selectedBandwidthId || 0, 10)
                        );

                        if (!currentPlan || !selectedPlan) {
                          return <p>Unable to calculate price difference.</p>;
                        }

                        const currentBandwidthPrice = parseFloat(currentPlan.price || 0);
                        const newBandwidthPrice = parseFloat(selectedPlan.price || 0);
                        
                        const currentTotalPrice = parseFloat(server.monthly_price || server.price || 0);
                        const baseServerPrice = Math.max(0, currentTotalPrice - currentBandwidthPrice);
                        
                        const newTotalPrice = baseServerPrice + newBandwidthPrice;
                        const totalPriceDifference = newTotalPrice - currentTotalPrice;

                        const proratedAmount = calculateProratedAmount();
                        
                        const vatRate = vatInfo.vat_exempt ? 0 : (vatInfo.vat_rate || 0);
                        const vatAmount = proratedAmount * (vatRate / 100);

                        return (
                          <>
                            <div className="row mb-3">
                              <div className="col-md-6">
                                <h6 className="text-muted mb-2">Current Service</h6>
                                <div className="card">
                                  <div className="card-body p-2">
                                    <div><strong>Total Monthly Cost</strong></div>
                                    <div className="small text-muted mb-2">
                                      <div>Base Server: €{baseServerPrice.toFixed(2)}</div>
                                      <div>Bandwidth ({currentPlan.name}): €{currentBandwidthPrice.toFixed(2)}</div>
                                    </div>
                                    <div className="h6 mb-0 text-primary">€{currentTotalPrice.toFixed(2)}/month</div>
                                  </div>
                                </div>
                              </div>
                              <div className="col-md-6">
                                <h6 className="text-muted mb-2">After Upgrade</h6>
                                <div className="card border-success">
                                  <div className="card-body p-2">
                                    <div><strong>Total Monthly Cost</strong></div>
                                    <div className="small text-muted mb-2">
                                      <div>Base Server: €{baseServerPrice.toFixed(2)}</div>
                                      <div>Bandwidth ({selectedPlan.name}): €{newBandwidthPrice.toFixed(2)}</div>
                                    </div>
                                    <div className="h6 mb-0 text-success">€{newTotalPrice.toFixed(2)}/month</div>
                                  </div>
                                </div>
                              </div>
                            </div>
                            
                            <hr className="my-2" />
                            
                            <div className="d-flex justify-content-between mb-1">
                              <div>Monthly Price Increase:</div>
                              <div>€{totalPriceDifference.toFixed(2)}</div>
                            </div>
                            <div className="d-flex justify-content-between mb-1">
                              <div>Prorated Amount:</div>
                              <div>€{proratedAmount.toFixed(2)}</div>
                            </div>
                            {!vatInfo.vat_exempt && vatRate > 0 && (
                              <div className="d-flex justify-content-between mb-1">
                                <div>VAT ({vatRate.toFixed(1)}%{vatInfo.country ? ` - ${vatInfo.country}` : ''}):</div>
                                <div>€{vatAmount.toFixed(2)}</div>
                              </div>
                            )}
                            {vatInfo.vat_exempt && (
                              <div className="d-flex justify-content-between mb-1">
                                <div>VAT:</div>
                                <div>Exempt</div>
                              </div>
                            )}
                            <div className="d-flex justify-content-between mb-1">
                              <div><strong>Total Amount Due Now:</strong></div>
                              <div><strong>€{(proratedAmount + vatAmount).toFixed(2)}</strong></div>
                            </div>
                            <div className="small text-muted">
                              Prorated to next billing date ({server?.expires || 'N/A'}).
                              New monthly cost will be €{newTotalPrice.toFixed(2)} starting from next billing cycle.
                              {vatInfo.country && ` VAT based on ${vatInfo.country}.`}
                              {vatInfo.vat_exempt && ' VAT exempt status applied.'}
                            </div>
                          </>
                        );
                      })()}
                    </div>
                  </div>
                )}

                <div className="alert alert-info mb-2 py-2">
                  <i className="fa fa-info-circle me-1" style={{paddingRight: '0.5rem'}}></i>
                  <strong>Billing Process:</strong> Clicking "Generate Upgrade Invoice" will create an invoice for the prorated amount.
                  Your bandwidth will be upgraded after the invoice is paid.
                </div>

                <div className="alert alert-warning mb-2 py-2">
                  <i className="fa fa-exclamation-triangle me-1" style={{paddingRight: '0.5rem'}}></i>
                  <strong>Note:</strong> Upgrade to higher bandwidth plans only. Price difference prorated to billing date.
                  {cpuRestrictionInfo && ' Some bandwidth plans may not be available based on your server configuration.'}
                </div>
              </div>
            )}
          </MDBModalBody>

          <MDBModalFooter>
            <button
              className="btn btn-secondary"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              className="btn btn-success ms-2"
              onClick={handleBandwidthChange}
              disabled={
                isLoading ||
                isLoadingVat ||
                isSubmitting ||
                !selectedBandwidthId ||
                selectedBandwidthId === currentBandwidthId ||
                !bandwidthPlans ||
                bandwidthPlans.length === 0
              }
            >
              {isSubmitting ? (
                <>
                  <span className="spinner-border spinner-border-sm me-1" role="status"></span>
                  Processing...
                </>
              ) : (
                <>Generate Upgrade Invoice</>
              )}
            </button>
          </MDBModalFooter>
        </MDBModalContent>
      </MDBModalDialog>
    </MDBModal>
  );
};

export default BandwidthUpgradeModal;