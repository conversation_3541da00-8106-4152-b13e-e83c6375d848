import React, { useState, useEffect, useCallback } from "react";
import {
  MDBModal,
  MDBModalDialog,
  MDBModalContent,
  MDBModalHeader,
  MDBModalTitle,
  MDBModalBody,
  MDBModalFooter,
} from 'mdb-react-ui-kit';

/**
 * VpsUpgradeModal Component
 *
 * A reusable modal component for upgrading VPS server plans.
 *
 * @param {Object} props - Component props
 * @param {boolean} props.isOpen - Controls whether the modal is displayed
 * @param {Function} props.onClose - Function to call when the modal is closed
 * @param {Object} props.server - Server data object
 * @param {Function} props.onSuccess - Function to call after successful plan upgrade
 */
const VpsUpgradeModal = ({ isOpen, onClose, server, onSuccess }) => {
  // State for VPS plans and selection
  const [vpsPlans, setVpsPlans] = useState([]);
  const [selectedPlanId, setSelectedPlanId] = useState(null);
  const [currentPlanId, setCurrentPlanId] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [debugInfo, setDebugInfo] = useState({});
  
  // VAT-related state
  const [vatInfo, setVatInfo] = useState({
    vat_rate: 0,
    vat_exempt: false,
    country: null,
    vat_id: null
  });
  const [isLoadingVat, setIsLoadingVat] = useState(false);

  // Fetch VAT information
  const fetchVatInfo = useCallback(async () => {
    setIsLoadingVat(true);
    try {
      const token = sessionStorage.getItem('token');
      const response = await fetch('/api.php?f=get_vat_rate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      const responseText = await response.text();
      console.log("Raw VAT rate response:", responseText);

      let data;
      try {
        data = JSON.parse(responseText);
        console.log("Parsed VAT data:", data);
      } catch (e) {
        console.error("Failed to parse VAT response:", e);
        return;
      }

      if (data && data.success) {
        setVatInfo({
          vat_rate: parseFloat(data.vat_rate) || 0,
          vat_exempt: data.vat_exempt || false,
          country: data.country || null,
          vat_id: data.vat_id || null
        });
        console.log("VAT info set:", {
          vat_rate: parseFloat(data.vat_rate) || 0,
          vat_exempt: data.vat_exempt || false,
          country: data.country || null
        });
      }
    } catch (error) {
      console.error("Error fetching VAT info:", error);
    } finally {
      setIsLoadingVat(false);
    }
  }, []);

  // Calculate prorated amount for the upgrade
  const calculateProratedAmount = useCallback(() => {
    // Skip calculation if required values are missing
    if (!selectedPlanId || !currentPlanId ||
        selectedPlanId === currentPlanId ||
        !vpsPlans || !vpsPlans.length) {
      return 0;
    }

    // Convert IDs to numbers for consistent comparison
    const selectedId = parseInt(selectedPlanId, 10);
    const currentId = parseInt(currentPlanId, 10);
    
    // Find current and selected VPS plans
    const currentPlan = vpsPlans.find(plan => parseInt(plan.id, 10) === currentId);
    const selectedPlan = vpsPlans.find(plan => parseInt(plan.id, 10) === selectedId);

    if (!currentPlan || !selectedPlan) {
      console.log(`Plans not found - currentId: ${currentId}, selectedId: ${selectedId}`);
      console.log("Available plans:", vpsPlans);
      return 0;
    }

    // Calculate price difference
    const currentPrice = parseFloat(currentPlan.price || 0);
    const newPrice = parseFloat(selectedPlan.price || 0);
    const priceDifference = newPrice - currentPrice;

    if (priceDifference <= 0) {
      return 0;
    }

    // Calculate remaining days until due date
    const today = new Date();
    const dueDate = server?.next_renewal ? new Date(server.next_renewal) : null;

    if (!dueDate) {
      return priceDifference; // If no due date, charge full amount
    }

    const daysRemaining = Math.max(0, Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24)));

    // Calculate prorated amount: (new_price - current_price) / 30 * days_remaining
    const proratedAmount = (priceDifference / 30) * daysRemaining;

    return Math.round(proratedAmount * 100) / 100; // Round to 2 decimal places
  }, [selectedPlanId, currentPlanId, vpsPlans, server]);

  // Fetch VPS plans from API
  const fetchVpsPlans = useCallback(async () => {
    if (!server || !server.id) return;

    setIsLoading(true);
    setError('');
    
    try {
      const token = sessionStorage.getItem('token');
      const response = await fetch('/api.php?f=get_vps_plans', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      const responseText = await response.text();
      console.log("Raw VPS plans response:", responseText);

      let data;
      try {
        data = JSON.parse(responseText);
        console.log("Parsed VPS plans:", data);
      } catch (e) {
        console.error("Failed to parse VPS plans response:", e);
        setError('Invalid response from server');
        return;
      }

      if (data && Array.isArray(data)) {
        // Convert all IDs to numbers for consistency
        const plansWithNumericIds = data.map(plan => ({
          ...plan,
          id: parseInt(plan.id, 10),
          price: parseFloat(plan.price || 0)
        }));

        // Sort plans by price for easier comparison
        plansWithNumericIds.sort((a, b) => a.price - b.price);
        setVpsPlans(plansWithNumericIds);

        // Determine the current plan ID
        let currentId = null;

        // Try to find current plan by matching configuration details
        if (server && server.plan_id) {
          currentId = parseInt(server.plan_id, 10);
          console.log("Using plan_id from server:", currentId);
        } else if (server && server.label) {
          // Try to match by label
          const matchingPlan = plansWithNumericIds.find(plan => 
            plan.label && plan.label.toLowerCase() === server.label.toLowerCase()
          );
          if (matchingPlan) {
            currentId = matchingPlan.id;
            console.log("Found matching plan by label:", currentId);
          }
        }

        // If we still don't have a current ID, try to match by specs
        if (!currentId && server) {
          const matchingPlan = plansWithNumericIds.find(plan => {
            // Try to match by memory, storage, etc.
            const memoryMatch = server.ram && plan.Memory && 
              server.ram.toLowerCase().includes(plan.Memory.toLowerCase().split(' ')[0]);
            const storageMatch = server.disk && plan.Storage && 
              server.disk.toLowerCase().includes(plan.Storage.toLowerCase().split(' ')[0]);
            
            return memoryMatch || storageMatch;
          });
          
          if (matchingPlan) {
            currentId = matchingPlan.id;
            console.log("Found matching plan by specs:", currentId);
          }
        }

        // If we still don't have a current ID, default to the first plan
        if (!currentId && plansWithNumericIds.length > 0) {
          currentId = plansWithNumericIds[0].id;
          console.log("Defaulting to first plan:", currentId);
        }

        // Set the current and selected plan IDs
        setCurrentPlanId(currentId);
        setSelectedPlanId(currentId);
      } else {
        setError('No VPS plans available');
      }
    } catch (error) {
      console.error("Error fetching VPS plans:", error);
      setError('Failed to load VPS plans. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [server]);

  // Handle VPS plan upgrade submission
  const handlePlanUpgrade = async () => {
    // Clear previous errors
    setError('');
    setDebugInfo({});

    // Basic validation
    if (!selectedPlanId) {
      setError('Please select a VPS plan');
      return;
    }

    if (!currentPlanId) {
      setError('Current plan information is missing');
      return;
    }

    // Convert IDs to numbers for consistent comparison
    const currentId = parseInt(currentPlanId, 10);
    const selectedId = parseInt(selectedPlanId, 10);

    // If they're the same plan, stop
    if (currentId === selectedId) {
      setError('Please select a different VPS plan');
      return;
    }

    // Safety check for vpsPlans
    if (!vpsPlans || !vpsPlans.length) {
      setError('VPS plan information is unavailable');
      return;
    }

    // Find the plans with consistent type handling
    console.log("Finding plans - Current ID:", currentId, "Selected ID:", selectedId);
    console.log("Available plans:", vpsPlans);
    
    // Find plans with integer comparison
    const currentPlan = vpsPlans.find(plan => parseInt(plan.id, 10) === currentId);
    const selectedPlan = vpsPlans.find(plan => parseInt(plan.id, 10) === selectedId);

    // If plans can't be found, show error and debug info
    if (!currentPlan || !selectedPlan) {
      console.error("Plan not found - Current:", !currentPlan, "Selected:", !selectedPlan);
      
      // Collect debug information
      const debugData = {
        currentId: currentId,
        selectedId: selectedId,
        vpsPlans: vpsPlans.map(p => ({ id: p.id, label: p.label })),
        server: {
          id: server?.id,
          plan_id: server?.plan_id,
          label: server?.label
        }
      };
      
      setDebugInfo(debugData);
      setError('Invalid VPS plan selection. Technical information has been logged for support.');
      return;
    }

    // Check if it's actually an upgrade
    if (selectedPlan.price <= currentPlan.price) {
      setError('You can only upgrade to a higher-tier VPS plan');
      return;
    }

    try {
      setIsSubmitting(true);
      setDebugInfo({
        stage: 'starting_request',
        currentPlan,
        selectedPlan
      });

      const token = sessionStorage.getItem('token');

      // Direct invoice generation approach
      const generateVpsUpgradeInvoice = async () => {
        try {
          console.log("Using direct invoice generation approach");
          setDebugInfo(prev => ({ ...prev, stage: 'direct_invoice_generation' }));

          // Calculate prorated amount
          const proratedAmount = calculateProratedAmount();
          setDebugInfo(prev => ({ ...prev, proratedAmount }));
          
          // Create a detailed description for better tracking
          const description = `VPS plan upgrade from ${currentPlan.label} to ${selectedPlan.label} for server ID ${server.id}`;
          
          // Use VAT info from state
          const vatRate = vatInfo.vat_exempt ? 0 : (vatInfo.vat_rate || 0);
          const vatAmount = proratedAmount * (vatRate / 100);
          
          // Create invoice payload with all required fields INCLUDING TAX INFO
          const invoicePayload = {
            token: sessionStorage.getItem('token'),
            amount: proratedAmount,
            order_id: server.order_id || server.id,
            type: "VPS Plan Upgrade",
            description: description,
            vat_rate: vatRate,
            vat_amount: vatAmount,
            server_expires: server.next_renewal,
            // Add hostname information directly from server object
            server_hostname: server.hostname || server.name || null,
            metadata: JSON.stringify({
              action: 'vps_plan_upgrade',
              server_id: server.id,
              current_plan_id: currentId,
              new_plan_id: selectedId,
              server_expires: server.next_renewal,
              // Include hostname in metadata as well for backup
              server_hostname: server.hostname || server.name || null
            })
          };
          
          console.log("Invoice generation payload:", invoicePayload);
          setDebugInfo(prev => ({ ...prev, invoicePayload }));
          
          // Generate the invoice
          const response = await fetch('/api.php?f=generate_invoice', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(invoicePayload)
          });
          
          const responseText = await response.text();
          console.log("Invoice generation response:", responseText);
          setDebugInfo(prev => ({ ...prev, rawResponse: responseText }));
          
          // Parse and process the response
          const data = JSON.parse(responseText);
          setDebugInfo(prev => ({ ...prev, parsedResponse: data }));
          
          if (data.success) {
            // Enhance the response with the plan information
            const enhancedResponse = {
              ...data,
              current_plan: currentPlan,
              new_plan: selectedPlan,
              prorated_amount: proratedAmount,
              vat_rate: vatRate,
              vat_amount: data.details?.tax || vatAmount,
              total_amount: data.details?.total || (proratedAmount + vatAmount),
              message: `VPS plan upgrade invoice generated successfully. Invoice #${data.invoice_id} has been created for ${selectedPlan.label}.`
            };
            
            console.log("Enhanced success response:", enhancedResponse);
            return enhancedResponse;
          }
          
          // Return the original error response if not successful
          return data;
        } catch (error) {
          console.error("Error generating VPS upgrade invoice:", error);
          setDebugInfo(prev => ({ 
            ...prev, 
            error: error.message,
            errorStack: error.stack
          }));
          
          return {
            success: false,
            error: "Error generating VPS upgrade invoice: " + error.message
          };
        }
      };

      // Check for server
      if (!server) {
        setError('Server information is missing');
        return;
      }

      // Check if we have server.id
      if (!server.id) {
        setError('Server ID information is missing');
        return;
      }

      // Generate the VPS upgrade invoice
      const data = await generateVpsUpgradeInvoice();

      // Check if the response is valid
      if (!data) {
        console.error("No data returned from API");
        setError('No response from server');
        return;
      }

      console.log("Processing API response:", data);
      setDebugInfo(prev => ({ ...prev, stage: 'processing_response', finalResponse: data }));

      if (data.success === true) {
        console.log("VPS plan upgrade invoice generated successfully");

        // Format the data for the success callback
        const formattedData = {
          ...data,
          message: data.message || "VPS plan upgrade request submitted. An invoice has been generated. Your plan will be upgraded after payment is processed."
        };

        // Call the success callback
        if (typeof onSuccess === 'function') {
          console.log("Calling onSuccess callback with data:", formattedData);
          onSuccess(formattedData);
        }

        // Close the modal
        onClose();
      } else {
        console.error("API returned error:", data.error || "Unknown error");

        // Provide more specific error messages based on common issues
        if (data.error && data.error.includes("Invalid")) {
          setError(
            "Failed to generate invoice. " +
            "Please contact support and mention server ID: " + server.id
          );
        } else if (data.error && data.error.includes("permission")) {
          setError(
            "You don't have permission to generate an invoice for this server. " +
            "Please contact support for assistance."
          );
        } else if (data.error && data.error.includes("not found")) {
          setError(
            "The server information could not be found. " +
            "Please contact support and mention server ID: " + server.id
          );
        } else {
          setError(data.error || 'Failed to generate VPS plan upgrade invoice');
        }
      }
    } catch (error) {
      console.error("Error upgrading VPS plan:", error);
      setError('Connection error - please try again');
      setDebugInfo(prev => ({ 
        ...prev, 
        fatalError: error.message,
        fatalErrorStack: error.stack
      }));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Load VPS plans and VAT info when the modal opens
  useEffect(() => {
    if (isOpen && server) {
      console.log("Modal opened with server data:", server);

      // Log important server fields for debugging
      console.log("Server ID:", server.id);
      console.log("Server plan_id:", server.plan_id);
      console.log("Server label:", server.label);

      // Reset any previous errors
      setError('');
      setDebugInfo({});

      // Reset loading state
      setIsLoading(true);

      // Fetch both VPS plans and VAT info
      fetchVpsPlans();
      fetchVatInfo();
    }
  }, [isOpen, server, fetchVpsPlans, fetchVatInfo]);

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setError('');
      setSelectedPlanId(null);
      setDebugInfo({});
      // Reset VAT info when modal closes
      setVatInfo({
        vat_rate: 0,
        vat_exempt: false,
        country: null,
        vat_id: null
      });
    }
  }, [isOpen]);

  return (
    <MDBModal show={isOpen} tabIndex='-1'>
      <MDBModalDialog>
        <MDBModalContent>
          <MDBModalHeader>
            <MDBModalTitle>VPS Plan Upgrade</MDBModalTitle>
            <button className="close" onClick={onClose}><span aria-hidden="true">×</span></button>
          </MDBModalHeader>

          <MDBModalBody>
            {(isLoading || isLoadingVat) ? (
              <div className="text-center py-4">
                <div className="spinner-border text-primary mb-3" role="status"></div>
                <p>Loading VPS plans and tax information...</p>
              </div>
            ) : (
              <div className="form-group">
                <label className="form-label mb-1">Select New VPS Plan:</label>
                <select
                  name="vps_plan"
                  className="form-control custom-select mb-2"
                  value={selectedPlanId || ''}
                  onChange={(e) => {
                    console.log("Selected value from dropdown:", e.target.value);
                    const newPlanId = parseInt(e.target.value, 10);
                    console.log("Parsed plan ID:", newPlanId);

                    if (!isNaN(newPlanId)) {
                      console.log("Setting selected plan ID to:", newPlanId);
                      setSelectedPlanId(newPlanId);
                      setError('');
                    }
                  }}
                  disabled={isSubmitting}
                >
                  <option value="" disabled>Select a VPS plan</option>
                  {vpsPlans.map(plan => {
  // Safely parse IDs with fallbacks
  const planId = parseInt(plan?.id || 0, 10);
  const currentId = parseInt(currentPlanId || 0, 10);

  console.log(`Rendering plan option: ID=${planId}, Label=${plan?.label}`);

  // Find current plan with null safety
  const currentPlan = vpsPlans.find(p => {
    const pId = parseInt(p?.id || 0, 10);
    return pId === currentId;
  });

  // Determine if this plan is an upgrade and calculate price difference
  let isUpgrade = false;
  let priceDifference = 0;

  if (currentPlan) {
    // If we have a current plan, compare prices
    const currentPrice = parseFloat(currentPlan.price || 0);
    const planPrice = parseFloat(plan?.price || 0);
    isUpgrade = planPrice > currentPrice;
    priceDifference = planPrice - currentPrice;
    console.log(`Comparing prices: Current=${currentPrice}, Plan=${planPrice}, isUpgrade=${isUpgrade}`);
  } else {
    // If we don't have a current plan, assume all plans are upgrades except the first one
    const planIndex = vpsPlans.findIndex(p => parseInt(p?.id || 0, 10) === planId);
    isUpgrade = planIndex > 0;
    console.log(`No current plan found. Plan index=${planIndex}, isUpgrade=${isUpgrade}`);
  }

  console.log(`Plan ${planId}: Current=${planId === currentId}, Upgrade=${isUpgrade}`);

  return (
    <option
      key={planId || `plan-${Math.random()}`}
      value={planId}
      disabled={planId === currentId || !isUpgrade}
    >
      {plan?.label || `Plan ${planId}`}
      {planId === currentId ? ' (Current)' :
        isUpgrade ? ` (+€${priceDifference.toFixed(2)}/mo)` :
        ' (Not an upgrade - unavailable)'}
    </option>
  );
})}
                </select>

                {error && (
                  <div className="alert alert-danger mb-2">
                    <i className="fa fa-exclamation-circle me-1" style={{paddingRight: '0.5rem'}}></i>
                    {error}
                  </div>
                )}

{selectedPlanId && selectedPlanId !== currentPlanId && (
  <div className="card mb-2">
    <div className="card-header bg-light py-1"><h5 className="mb-0">Plan Comparison & Price Summary</h5></div>
    <div className="card-body py-2">
      {(() => {
        // Find plans with null safety and consistent type conversion
        const currentPlan = vpsPlans.find(plan =>
          parseInt(plan?.id || 0, 10) === parseInt(currentPlanId || 0, 10)
        );
        const selectedPlan = vpsPlans.find(plan =>
          parseInt(plan?.id || 0, 10) === parseInt(selectedPlanId || 0, 10)
        );

        if (!currentPlan || !selectedPlan) {
          return <p>Unable to calculate price difference.</p>;
        }

        // For VPS, the plan price IS the total service price
        const currentTotalPrice = parseFloat(currentPlan.price || 0);
        const newTotalPrice = parseFloat(selectedPlan.price || 0);
        const totalPriceDifference = newTotalPrice - currentTotalPrice;

        // Calculate prorated amount
        const proratedAmount = calculateProratedAmount();
        
        // Use VAT info from state
        const vatRate = vatInfo.vat_exempt ? 0 : (vatInfo.vat_rate || 0);
        const vatAmount = proratedAmount * (vatRate / 100);

        return (
          <>
            <div className="row mb-3">
              <div className="col-md-6">
                <h6 className="text-muted mb-2">Current Plan</h6>
                <div className="card">
                  <div className="card-body p-2">
                    <div><strong>{currentPlan.label}</strong></div>
                    <div className="small text-muted mb-2">
                      {currentPlan.Memory && <div>RAM: {currentPlan.Memory}</div>}
                      {currentPlan.Storage && <div>Storage: {currentPlan.Storage}</div>}
                      {currentPlan.Bandwidth && <div>Bandwidth: {currentPlan.Bandwidth}</div>}
                    </div>
                    <div className="h6 mb-0 text-primary">€{currentTotalPrice.toFixed(2)}/month</div>
                  </div>
                </div>
              </div>
              <div className="col-md-6">
                <h6 className="text-muted mb-2">New Plan</h6>
                <div className="card border-success">
                  <div className="card-body p-2">
                    <div><strong>{selectedPlan.label}</strong></div>
                    <div className="small text-muted mb-2">
                      {selectedPlan.Memory && <div>RAM: {selectedPlan.Memory}</div>}
                      {selectedPlan.Storage && <div>Storage: {selectedPlan.Storage}</div>}
                      {selectedPlan.Bandwidth && <div>Bandwidth: {selectedPlan.Bandwidth}</div>}
                    </div>
                    <div className="h6 mb-0 text-success">€{newTotalPrice.toFixed(2)}/month</div>
                  </div>
                </div>
              </div>
            </div>
            
            <hr className="my-2" />
            
            <div className="d-flex justify-content-between mb-1">
              <div>Monthly Price Increase:</div>
              <div>€{totalPriceDifference.toFixed(2)}</div>
            </div>
            <div className="d-flex justify-content-between mb-1">
              <div>Prorated Amount:</div>
              <div>€{proratedAmount.toFixed(2)}</div>
            </div>
            {!vatInfo.vat_exempt && vatRate > 0 && (
              <div className="d-flex justify-content-between mb-1">
                <div>VAT ({vatRate.toFixed(1)}%{vatInfo.country ? ` - ${vatInfo.country}` : ''}):</div>
                <div>€{vatAmount.toFixed(2)}</div>
              </div>
            )}
            {vatInfo.vat_exempt && (
              <div className="d-flex justify-content-between mb-1">
                <div>VAT:</div>
                <div>Exempt</div>
              </div>
            )}
            <div className="d-flex justify-content-between mb-1">
              <div><strong>Total Amount Due Now:</strong></div>
              <div><strong>€{(proratedAmount + vatAmount).toFixed(2)}</strong></div>
            </div>
            <div className="small text-muted">
              Prorated to next billing date ({server?.next_renewal || 'N/A'}).
              New monthly cost will be €{newTotalPrice.toFixed(2)} starting from next billing cycle.
              {vatInfo.country && ` VAT based on ${vatInfo.country}.`}
              {vatInfo.vat_exempt && ' VAT exempt status applied.'}
            </div>
          </>
        );
      })()}
    </div>
  </div>
)}

                <div className="alert alert-info mb-2 py-2">
                  <i className="fa fa-info-circle me-1" style={{paddingRight: '0.5rem'}}></i>
                  <strong>Upgrade Process:</strong> Clicking "Generate Upgrade Invoice" will create an invoice for the prorated amount.
                  Your VPS plan will be upgraded after the invoice is paid.
                </div>

                <div className="alert alert-warning mb-2 py-2">
                  <i className="fa fa-exclamation-triangle me-1" style={{paddingRight: '0.5rem'}}></i>
                  <strong>Note:</strong> You can only upgrade to higher-tier plans. The price difference is prorated to your next billing date.
                </div>
              </div>
            )}
          </MDBModalBody>

          <MDBModalFooter>
            <button
              className="btn btn-secondary"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              className="btn btn-success ms-2"
              onClick={handlePlanUpgrade}
              disabled={
                isLoading ||
                isLoadingVat ||
                isSubmitting ||
                !selectedPlanId ||
                selectedPlanId === currentPlanId ||
                !vpsPlans ||
                vpsPlans.length === 0
              }
            >
              {isSubmitting ? (
                <>
                  <span className="spinner-border spinner-border-sm me-1" role="status"></span>
                  Processing...
                </>
              ) : (
                <>Generate Upgrade Invoice</>
              )}
            </button>
          </MDBModalFooter>
        </MDBModalContent>
      </MDBModalDialog>
    </MDBModal>
  );
};

export default VpsUpgradeModal;