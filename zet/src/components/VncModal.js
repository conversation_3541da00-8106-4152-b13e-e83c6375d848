import React, { useState } from 'react';
import {
  MDBModal,
  MDBModalDialog,
  MDBModalContent,
  MDBModalHeader,
  MDBModalTitle,
  MDBModalBody,
  MDBModalFooter,
  MDBBtn,
  MDBSpinner
} from 'mdb-react-ui-kit';
import SimpleVncViewer from './SimpleVncViewer';
import PropTypes from 'prop-types';

const VncModal = ({ isOpen, toggle, vncDetails, serverId }) => {
  const [connectionStatus, setConnectionStatus] = useState('connecting');
  const [error, setError] = useState(null);

  // Parse VNC connection details
  const parseVncUrl = (url) => {
    try {
      if (!url) return null;

      // Try to parse as a WebSocket URL first
      if (url.startsWith('ws://') || url.startsWith('wss://')) {
        const wsUrl = new URL(url);
        return {
          host: wsUrl.hostname,
          port: wsUrl.port || (wsUrl.protocol === 'wss:' ? 443 : 80),
          path: wsUrl.pathname.substring(1) // Remove leading slash
        };
      }

      // Try to parse as HTTP URL (which might contain WebSocket info)
      if (url.startsWith('http://') || url.startsWith('https://')) {
        const httpUrl = new URL(url);
        // Extract WebSocket details from URL or query parameters
        // This is a simplified approach - actual implementation depends on SolusVM's URL format
        return {
          host: httpUrl.hostname,
          port: httpUrl.port || (httpUrl.protocol === 'https:' ? 443 : 80),
          path: httpUrl.pathname.substring(1) // Remove leading slash
        };
      }

      // If it's just a hostname:port format
      if (url.includes(':')) {
        const [host, port] = url.split(':');
        return { host, port: parseInt(port, 10), path: '' };
      }

      // Default fallback - assume it's just a hostname
      return { host: url, port: 80, path: '' };
    } catch (err) {
      console.error('Error parsing VNC URL:', err);
      setError('Invalid VNC connection URL');
      return null;
    }
  };

  const handleConnect = () => {
    setConnectionStatus('connected');
    setError(null);
  };

  const handleDisconnect = (e) => {
    setConnectionStatus('disconnected');
    if (e && e.detail && e.detail.clean === false) {
      setError('Connection closed unexpectedly');
    }
  };

  const handleError = (err) => {
    setConnectionStatus('error');
    setError(err.message || 'Failed to connect to VNC server');
  };

  // Extract connection details
  const connectionDetails = vncDetails?.vnc_url ? parseVncUrl(vncDetails.vnc_url) : null;
  const password = vncDetails?.vnc_password || '';

  // Check if we have valid VNC details
  const hasValidVncDetails = !!vncDetails && (!!vncDetails.vnc_url || !!vncDetails.fallback_url);

  return (
    <MDBModal show={isOpen} tabIndex='-1' staticBackdrop>
      <MDBModalDialog size='fullscreen'>
        <MDBModalContent>
          <MDBModalHeader>
            <MDBModalTitle>VNC Console - Server #{serverId}</MDBModalTitle>
            <MDBBtn className='btn-close' color='none' onClick={toggle}></MDBBtn>
          </MDBModalHeader>
          <MDBModalBody style={{ padding: 0, height: 'calc(100vh - 132px)', overflow: 'hidden' }}>
            {connectionStatus === 'connecting' && (
              <div className="d-flex justify-content-center align-items-center h-100">
                <div className="text-center">
                  <MDBSpinner role='status'>
              
                  </MDBSpinner>
                  <p className="mt-2">Connecting to VNC server...</p>
                </div>
              </div>
            )}





            {hasValidVncDetails && !error && (
              <SimpleVncViewer
                vncDetails={{
                  ...vncDetails,
                  server_id: serverId
                }}
                onConnect={handleConnect}
                onError={handleError}
              />
            )}
          </MDBModalBody>
          <MDBModalFooter>
            <MDBBtn color='secondary' onClick={toggle}>
              Close
            </MDBBtn>
            {connectionStatus === 'connected' && (
              <div className="text-success me-auto">
                <i className="fa fa-check-circle me-2"></i>
                Connected
              </div>
            )}
            {connectionStatus === 'disconnected' && !error && (
              <div className="text-warning me-auto">
                <i className="fa fa-exclamation-circle me-2"></i>
                Disconnected
              </div>
            )}
          </MDBModalFooter>
        </MDBModalContent>
      </MDBModalDialog>
    </MDBModal>
  );
};

VncModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  toggle: PropTypes.func.isRequired,
  vncDetails: PropTypes.object,
  serverId: PropTypes.oneOfType([PropTypes.number, PropTypes.string])
};

export default VncModal;
