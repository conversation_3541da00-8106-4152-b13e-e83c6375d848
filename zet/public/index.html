<!DOCTYPE html>
<html lang="en" dir="ltr">
	<head>

		<!-- Meta data -->
		<meta charset="UTF-8">
		<meta name='viewport' content='width=device-width, initial-scale=1.0, user-scalable=0'>
		<meta content="ZetServers - Administration Panel" name="description">
		<meta content="INTERKVM HOST SRL" name="author">
		<meta name="keywords" content="Admin, Admin Template, Dashboard, Responsive, Admin Dashboard, Bootstrap, Bootstrap 4, Clean, Backend, Jquery, Modern, Web App, Admin Panel, Ui, Premium Admin Templates, Flat, Admin Theme, Ui Kit, Bootstrap Admin, Responsive Admin, Application, Template, Admin Themes, Dashboard Template"/>

		<!-- Title -->
		<title>ZetServers - Administration Panel</title>

		<!--Favicon -->
		<link rel="apple-touch-icon" sizes="180x180" href="/assets/images/apple-touch-icon.png">
		<link rel="icon" type="image/png" sizes="32x32" href="/assets/images/favicon-32x32.png">
		<link rel="icon" type="image/png" sizes="16x16" href="/assets/images/favicon-16x16.png">
		<link rel="manifest" href="/assets/images/site.webmanifest">
		<link rel="mask-icon" href="/assets/images/safari-pinned-tab.svg" color="#5bbad5">
		<meta name="msapplication-TileColor" content="#ffffff">
		<meta name="theme-color" content="#ffffff">

		<!-- Bootstrap css -->
		<link href="/assets/plugins/bootstrap/css/bootstrap.css" rel="stylesheet" />

		<!-- Style css -->
		<link href="/assets/css/style.css" rel="stylesheet" />

		<!-- Dark css -->
		<link href="/assets/css/dark.css" rel="stylesheet" />

		<!-- Skins css -->
		<link href="/assets/css/skins.css" rel="stylesheet" />

		<!-- Animate css -->
		<link href="/assets/css/animated.css" rel="stylesheet" />

		<!--Sidemenu css -->
                <link id="theme" href="/assets/css/sidemenu3.css" rel="stylesheet">

		<!-- P-scroll bar css-->
		<link href="/assets/plugins/p-scrollbar/p-scrollbar.css" rel="stylesheet" />

		<!---Icons css-->
		<link href="/assets/plugins/web-fonts/icons.css" rel="stylesheet" />
		<link href="/assets/plugins/web-fonts/font-awesome/font-awesome.min.css" rel="stylesheet">
		<link href="/assets/plugins/web-fonts/plugin.css" rel="stylesheet" />

		<!---jvectormap css-->
		<link href="/assets/plugins/jvectormap/jqvmap.css" rel="stylesheet" />


		<!--Daterangepicker css-->
		<link href="/assets/plugins/bootstrap-daterangepicker/daterangepicker.css" rel="stylesheet" />


		<link href="/assets/plugins/cookies/accept-cookies.css" rel="stylesheet" />
		<link href="/assets/plugins/cookies/cookies.css" rel="stylesheet" />



		<script src="/assets/js/vendors/jquery-3.5.1.min.js"></script>


		<script src="/assets/plugins/bootstrap/popper.min.js"></script>
		<script src="/assets/plugins/bootstrap/js/bootstrap.min.js"></script>


		<script src="/assets/plugins/othercharts/jquery.sparkline.min.js"></script>


		<script src="/assets/js/vendors/circle-progress.min.js"></script>


		<script src="/assets/plugins/rating/jquery.rating-stars.js"></script>


		<script src="/assets/plugins/sidemenu/sidemenu.js"></script>


		<script src="/assets/plugins/p-scrollbar/p-scrollbar.js"></script>
		<!-- Commented out due to initialization error -->
		<!-- <script src="/assets/plugins/p-scrollbar/p-scroll1.js"></script> -->


		<script src="/assets/plugins/counters/counterup.min.js"></script>
		<script src="/assets/plugins/counters/waypoints.min.js"></script>


		<script src="/assets/plugins/chart/chart.bundle.js"></script>
		<script src="/assets/plugins/chart/utils.js"></script>


	<!--- TABS JS -->
	<script src="/assets/plugins/tabs/jquery.multipurpose_tabcontent.js"></script>
	<script src="/assets/plugins/tabs/tab-content.js"></script>
		<script src="/assets/js/custom.js"></script>

	<style>
		.app-sidebar3 {
		  position: fixed !important;
		  height: 100vh !important;
		  overflow-y: auto !important;
		  z-index: 1000; /* Ensure it's above other content */
		}

		.app-content.main-content {
		  margin-left: 250px !important; /* Adjust this value based on actual sidebar width */
		}

		/* Optional: Add a transition for smoother open/close if the sidebar has that functionality */
		.app-sidebar3, .app-content.main-content {
		  transition: margin-left 0.3s ease, transform 0.3s ease;
		}
	</style>

	</head>
	<body class="app sidebar-mini light-mode default-sidebar sidenav-toggled">

    		<noscript>You need to enable JavaScript to run this app.</noscript>
    		<div id="root"></div>
	</body>
</html>
